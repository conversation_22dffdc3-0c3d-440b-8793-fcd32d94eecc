#ifndef CONFIG_H
#define CONFIG_H

// STM32F407VET6平衡车项目统一配置文件
// 集中管理所有系统参数定义，便于调试和维护

// ==================== 电机控制配置 ====================
#define MOTOR_PWM_MAX           1000    // PWM最大值(对应TIM3 ARR)
#define MOTOR_SPEED_MAX         1000    // 电机最大速度值
#define MOTOR_DEADZONE          50      // 电机死区，避免低速抖动
#define PWM_COMPENSATION_BASE   90      // 基础PWM补偿值

// ==================== 小角度补偿配置 ====================
#define SMALL_ANGLE_THRESHOLD   2.1f    // 小角度补偿阈值(度)
#define SMALL_ANGLE_PID_ENABLE  1       // 启用小角度PID补偿(1=启用,0=禁用)
#define SMALL_ANGLE_MIN_ERROR   0.5f    // 小角度补偿最小误差阈值(度)

// 小角度PID默认参数
#define SMALL_ANGLE_DEFAULT_KP  50.0f   // 小角度PID默认Kp值
#define SMALL_ANGLE_DEFAULT_KI  0.0f    // 小角度PID默认Ki值
#define SMALL_ANGLE_DEFAULT_KD  10.0f   // 小角度PID默认Kd值
#define SMALL_ANGLE_OUTPUT_MAX  200.0f  // 小角度PID输出限幅最大值
#define SMALL_ANGLE_OUTPUT_MIN  -200.0f // 小角度PID输出限幅最小值

// ==================== WiFi指令配置 ====================
// 电机控制指令(0-4)
#define WIFI_CMD_STOP           '0'     // 停止
#define WIFI_CMD_FORWARD        '1'     // 前进
#define WIFI_CMD_BACKWARD       '2'     // 后退
#define WIFI_CMD_LEFT           '3'     // 左转
#define WIFI_CMD_RIGHT          '4'     // 右转

// 传感器控制指令(5-7)
#define WIFI_CMD_AVOID_ON       '5'     // 开启避障模式
#define WIFI_CMD_FOLLOW_ON      '6'     // 开启跟随模式
#define WIFI_CMD_ALL_OFF        '7'     // 关闭所有功能

// 系统控制指令(8-9) - 新增
#define WIFI_CMD_CALIBRATE      '8'     // 机械中值校准
#define WIFI_CMD_MOTOR_TOGGLE   '9'     // 电机开关切换

// ==================== PID控制配置 ====================
// 平衡PID默认参数
#define DEFAULT_BALANCE_KP      15.0f   // 默认平衡PID Kp值
#define DEFAULT_BALANCE_KI      0.0f    // 默认平衡PID Ki值
#define DEFAULT_BALANCE_KD      8.0f    // 默认平衡PID Kd值
#define DEFAULT_BALANCE_MAX     800.0f  // 默认平衡PID输出限幅

// 速度PID默认参数
#define DEFAULT_VELOCITY_KP     0.0f    // 默认速度PID Kp值
#define DEFAULT_VELOCITY_KI     0.0f    // 默认速度PID Ki值
#define DEFAULT_VELOCITY_KD     0.0f    // 默认速度PID Kd值
#define DEFAULT_VELOCITY_MAX    15.0f   // 默认速度PID输出限幅

// 转向PID默认参数
#define DEFAULT_TURN_KP         0.0f    // 默认转向PID Kp值
#define DEFAULT_TURN_KI         0.0f    // 默认转向PID Ki值
#define DEFAULT_TURN_KD         0.0f    // 默认转向PID Kd值
#define DEFAULT_TURN_MAX        800.0f  // 默认转向PID输出限幅

// ==================== 超声波传感器配置 ====================
#define ULTRASONIC_TIMEOUT      30000   // 超时时间(μs) - 对应最大测距5m
#define ULTRASONIC_SOUND_SPEED  340.0f  // 声速(m/s)
#define ULTRASONIC_MAX_DISTANCE 400.0f  // 最大测距(cm)
#define ULTRASONIC_MIN_DISTANCE 2.0f    // 最小测距(cm)

// 避障和跟随参数
#define OBSTACLE_THRESHOLD      20.0f   // 避障阈值(cm)
#define FOLLOW_TARGET_DISTANCE  30.0f   // 跟随目标距离(cm)
#define FOLLOW_TOLERANCE        5.0f    // 跟随距离容差(cm)
#define TURN_SPEED              200     // 避障转向速度

// ==================== 系统配置 ====================
// 控制周期配置
#define MOTOR_TASK_PERIOD       10      // 电机任务周期(ms)
#define SENSOR_TASK_PERIOD      10      // 传感器任务周期(ms)
#define ULTRASONIC_TASK_PERIOD  50      // 超声波任务周期(ms)

// 调试配置
#define DEBUG_OUTPUT_ENABLE     1       // 启用调试输出(1=启用,0=禁用)
#define DEBUG_OUTPUT_INTERVAL   100     // 调试输出间隔(循环次数)

// WiFi配置
#define WIFI_CMD_TIMEOUT        500     // WiFi指令超时时间(ms)
#define WIFI_RETRY_MAX          3       // WiFi重试最大次数

// ==================== 错误码配置 ====================
#define ESP_ERROR               -00010  // ESP模块错误
#define MPU_ERROR               -00020  // MPU6050错误
#define PID_ERROR               -00030  // PID控制错误
#define UI_ERROR                -00040  // UI显示错误
#define KEY_ERROR               -00050  // 按键错误
#define CONFIG_ERROR            -00060  // 配置错误

#endif //CONFIG_H
