#include "uart.h"
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

uint8_t uart_rx_dma_buffer[128] = {0};
uint8_t uart3_rx_dma_buffer[64] = {0};

uint16_t uart3_rx_index_local = 0;
uint8_t uart3_dma_process_buffer[64] = {0};

int my_printf(UART_HandleTypeDef *huart_handle, const char *format_str, ...)
{
    char print_buffer[512];
    va_list argument_list;
    int output_len;
    va_start(argument_list, format_str);
    output_len = vsnprintf(print_buffer, sizeof(print_buffer), format_str, argument_list);
    va_end(argument_list);
    HAL_UART_Transmit(huart_handle, (uint8_t *)print_buffer, (uint16_t)output_len, HAL_MAX_DELAY);
    return output_len;
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart_handle, uint16_t recv_size)
{
    if (huart_handle->Instance == USART1)
    {
        // UART1接收到数据，直接回显（用于调试监控）
        uart_rx_dma_buffer[recv_size]='\0';
        HAL_UART_Transmit_DMA(&huart1, (uint8_t *)uart_rx_dma_buffer, recv_size);
    }
    if (huart_handle->Instance == USART3) // ESP01S数据处理
    {
        HAL_UART_DMAStop(huart_handle);

        uart3_rx_index_local = recv_size;

        memcpy(uart3_dma_process_buffer, uart3_rx_dma_buffer, recv_size);
        uart3_dma_process_buffer[recv_size] = '\0';

        memset(uart3_rx_dma_buffer, 0, sizeof(uart3_rx_dma_buffer));

        if (osMessageQueuePut(WiFiCmdQueueHandle,uart3_dma_process_buffer,0,0)==osOK)
            HAL_UART_Transmit_DMA(&huart1, (uint8_t *)uart3_dma_process_buffer, recv_size);
        else {
            my_printf(&huart1, "there has something wrong with the wifi-queue\r\n");
            osMessageQueueReset(WiFiCmdQueueHandle);
        }

        memset(uart3_dma_process_buffer, 0, sizeof(uart3_dma_process_buffer));

        HAL_UARTEx_ReceiveToIdle_DMA(&huart3, uart3_rx_dma_buffer, sizeof(uart3_rx_dma_buffer));

        __HAL_DMA_DISABLE_IT(&hdma_usart3_rx, DMA_IT_HT);
    }
}

void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1)
    {
        // // UART1发送完成，重新启动接收
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));
        HAL_UARTEx_ReceiveToIdle_DMA(huart, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
    }
}
