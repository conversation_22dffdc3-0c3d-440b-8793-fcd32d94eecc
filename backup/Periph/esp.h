#ifndef __esp_H
#define __esp_H

#include "sys.h"

typedef enum {
	STATE_AT_INIT,      // 发送AT测试指令
	STATE_AT_CWMODE,    // 设置WiFi模式
	STATE_AT_CWDHCP,    // Station模式
	STATE_AT_CWJAP,     // 连接WiFi
	STATE_AT_CIPSTART,	//配置TCP连接
	STATE_AT_CIPMODE,	//CIP模式
	STATE_OK,			// 连接完成
	STATE_ERROR,        // 错误状态
	NULL_STATE          // 空状态
  } AT_State;

// 定义一个结构体来传递预期的响应关键字和超时时间
typedef struct {
	const char* success_keyword; // 成功的关键字，例如 "OK"
	const char* failure_keyword; // 失败的关键字，例如 "ERROR" 或 "FAIL"
	uint32_t total_timeout_ms;   // 总的等待时间（毫秒）
} AT_Response_Config_t;

// 函数声明
AT_State get_wifi_state(void);  // 获取当前WiFi状态
uint8_t is_wifi_ready(void);    // 检查WiFi是否准备就绪

#endif
