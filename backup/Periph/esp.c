#include "esp.h"
#include "string.h"

static AT_State wifi_state=STATE_AT_INIT;
static int8_t retry = 0;
static int8_t retry_flag = 0;

// 发送AT指令（带\r\n）
void Send_AT_Command(const char* cmd)
{
    // 发送到ESP01S
    my_printf(&huart3, cmd);
}

// 它只负责从队列中取出一个完整的消息（如果存在），不再进行超时等待
int Rec_AT_Response_NonBlocking(char* buffer) {
    // 尝试立即从队列获取消息，超时设置为 0
    osStatus_t status = osMessageQueueGet(WiFiCmdQueueHandle, buffer, 0, 0);

    if (status == osOK) {
        // my_printf(&huart1, "ESP Rx: %s\r\n", buffer); // 打印收到的数据
        return 0; // 成功获取到消息
    } else {
        // 没有消息或发生错误，不打印 Timeout，因为这由上层逻辑判断
        return -1; // 没有消息或失败
    }
}

// 2. 引入 Wait_AT_Response 函数，负责循环等待和检查响应
// 这个函数会阻塞等待，直到收到预期响应、失败响应或总超时
int Wait_AT_Response(const AT_Response_Config_t* config, char* received_buffer) {
    uint32_t start_time = osKernelGetTickCount(); // 获取当前系统时间

    // 清空接收缓冲区
    memset(received_buffer, 0, 128); // 假设你的接收缓冲区最大128

    while (osKernelGetTickCount() - start_time < config->total_timeout_ms) {
        char current_response[128]; // 临时缓冲区，用于从队列获取单条消息
        memset(current_response, 0, sizeof(current_response));

        // 尝试从队列中获取消息，非阻塞
        if (Rec_AT_Response_NonBlocking(current_response) == 0) {
            // 成功获取到一条消息，将其追加到总的接收缓冲区中
            strcat(received_buffer, current_response);

            // 检查收到的数据是否包含成功关键字
            if (config->success_keyword && strstr(received_buffer, config->success_keyword)) {
                return 0; // 收到成功响应
            }
            // 检查收到的数据是否包含失败关键字
            if (config->failure_keyword && strstr(received_buffer, config->failure_keyword)) {
                return -1; // 收到失败响应
            }
        }
        // 短暂延迟，让出 CPU 给其他任务，并等待下一个字符到来
        osDelay(10); // 可以是 1ms, 5ms, 10ms，取决于你对响应速度的要求和系统负载
    }

    // 超时，没有收到预期响应
    my_printf(&huart1, "AT Response Timeout for keyword '%s'.\r\n", config->success_keyword ? config->success_keyword : "N/A");
    return -2; // 表示超时
}

// 3. 修改 Retry_AT_Command，使其调用 Wait_AT_Response
int Retry_AT_Command(const char* cmd, int retry_times, const AT_Response_Config_t* config) {
    char total_received_buffer[128]; // 用于存储所有收到的响应，以便 strstr 检查
    for (int i = 0; i < retry_times; i++) {
        my_printf(&huart1, "Sending AT Cmd: %s (Retry %d/%d)\r\n", cmd, i + 1, retry_times);
        Send_AT_Command(cmd); // 发送 AT 命令

        int status = Wait_AT_Response(config, total_received_buffer);

        if (status == 0) { // 成功收到预期响应
            my_printf(&huart1, "AT Cmd '%s' OK. Response: %s\r\n", cmd, total_received_buffer);
            return 0;
        } else if (status == -1) { // 收到失败响应
            my_printf(&huart1, "AT Cmd '%s' Failed (Received Failure Keyword). Response: %s\r\n", cmd, total_received_buffer);
            return -1;
        } else if (status == -2) { // 超时
            my_printf(&huart1, "AT Cmd '%s' Timed out. Response: %s\r\n", cmd, total_received_buffer);
        }

        // 如果不是最后一次尝试，并且没有成功，可以稍微延迟一下再重试
        if (i < retry_times - 1) {
            osDelay(200); // 每次重试之间稍微长一点的延迟
        }
    }
    my_printf(&huart1, "AT Cmd '%s' failed after %d retries.\r\n", cmd, retry_times);
    return -1; // 所有重试都失败
}

// 4. 修改 Wifi_Init 状态机
int Wifi_Init() {
    char wifi_response_buffer[128]; // 用于接收完整的响应
    static int wifi_send_count = 0;

    switch (wifi_state) {
        case STATE_AT_INIT: {
            AT_Response_Config_t config = {"OK", "ERROR", 2000}; // AT 命令等待 2 秒
            if (Retry_AT_Command("AT\r\n", 3, &config) == 0) {
                wifi_state = STATE_AT_CWMODE;
            } else {
                wifi_state = STATE_ERROR;
            }
            break;
        }
        case STATE_AT_CWMODE: {
            AT_Response_Config_t config = {"OK", "ERROR", 3000}; // CWMODE 等待 3 秒
            if (Retry_AT_Command("AT+CWMODE=1\r\n", 3, &config) == 0) {
                wifi_state = STATE_AT_CWDHCP;
            } else {
                wifi_state = STATE_ERROR;
            }
            break;
        }
        case STATE_AT_CWDHCP: {
            AT_Response_Config_t config = {"OK", "ERROR", 3000}; // CWDHCP 等待 3 秒
            if (Retry_AT_Command("AT+CWDHCP=1,1\r\n", 3, &config) == 0) {
                wifi_state = STATE_AT_CWJAP;
            } else {
                wifi_state = STATE_ERROR;
            }
            break;
        }
        case STATE_AT_CWJAP: {
            AT_Response_Config_t config = {"OK", "FAIL", 15000}; // 连接AP，最长等待 15 秒
            if (Retry_AT_Command("AT+CWJAP=\"Xiaomi 14\",\"ejwqtt6886\"\r\n", 3, &config) == 0) {
                wifi_state = STATE_AT_CIPSTART;
            } else {
                wifi_state = STATE_ERROR;
            }
            break;
        }
        case STATE_AT_CIPSTART: {
            AT_Response_Config_t config = {"OK", "ERROR", 10000}; // 建立TCP连接，最长等待 10 秒
            if (Retry_AT_Command("AT+CIPSTART=\"TCP\",\"*************\",8080\r\n", 3, &config) == 0) {
                wifi_state = STATE_AT_CIPMODE;
            } else {
                wifi_state = STATE_ERROR;
            }
            break;
        }
        case STATE_AT_CIPMODE: {
            AT_Response_Config_t config = {"OK", "ERROR", 3000}; // CIPMODE 等待 3 秒
            if (Retry_AT_Command("AT+CIPMODE=1\r\n", 3, &config) == 0) {
                wifi_state = STATE_OK;
            } else {
                wifi_state = STATE_ERROR;
            }
            break;
        }
        case STATE_OK: {
            if (wifi_send_count == 0) {
                // 第一次发送数据，准备进入透传模式
                wifi_send_count++;
                Send_AT_Command("AT+CIPSEND\r\n"); // 准备进入透传模式
                my_printf(&huart1, "WiFi initialized successfully and ready for data transmission!\r\n");
                }
            return 0; // 成功，结束本次 Wifi_Init 调用
        }
        case STATE_ERROR: {
            my_printf(&huart1, "WiFi initialization error occurred!\r\n",retry);
            Send_AT_Command("AT+RST\r\n");
            return -1; // 错误，结束本次 Wifi_Init 调用
        }
    }
    return 0; // 默认返回 0，确保函数有返回值
}

// StartSerialTask 保持不变，它会在每次循环中调用 Wifi_Init()
void StartSerialTask(void *argument) {
    osDelay(4000); // 第一次启动等待模块稳定

    for(;;) {
        int ret = Wifi_Init(); // 每次循环执行一次状态机的推进
        if (ret == 0) { // 如果 Wifi_Init 返回 0 (成功)
            // WiFi 模块可能已经成功初始化，现在可以进入数据交互阶段
            osDelay(1000); // 简单起见，暂时保持延时，让任务不会高速运行
        } else if (retry < 5){ // 如果 Wifi_Init 返回 -1 (失败)
            retry++;
            retry_flag = 1;
            my_printf(&huart1, "WiFi initialization failed! \r\nRetry times: %d/5 \r\nRetrying in 5 seconds...\r\n",retry);
            osDelay(5000); // 等待 5 秒后重试整个初始化流程
            wifi_state = STATE_AT_INIT; // 错误后重置状态机到初始状态
        }
        else if (retry >= 5 && retry_flag == 1){
            retry_flag = 0;
            my_printf(&huart1, "WiFi initialization completely failed!\r\n ");
            wifi_state = NULL_STATE; // 结束任务
        }
        // 每次状态机处理之间给其他任务一些时间，防止高速循环
        osDelay(100);
    }
}
/**
 * @brief 获取当前WiFi状态
 * @return 当前WiFi状态枚举值
 */
AT_State get_wifi_state(void) {
    return wifi_state;
}

/**
 * @brief 检查WiFi是否准备就绪
 * @return 1=WiFi已准备就绪，0=WiFi未准备就绪
 */
uint8_t is_wifi_ready(void) {
    return (wifi_state == STATE_OK) ? 1 : 0;
}