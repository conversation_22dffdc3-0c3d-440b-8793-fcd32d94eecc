#ifndef ENCODER_H
#define ENCODER_H

#include "sys.h"

#define ENCODER_PPR           13      // 编码器每转脉冲数
#define ENCODER_QUADRATURE    4        // 四倍频系数
#define ENCODER_SAMPLE_TIME   20       // 采样时间间隔(ms)
#define ENCODER_FILTER_SIZE   5        // 滤波窗口大小

typedef struct {
    int32_t total_count;               // 总计数值
    int32_t last_count;                // 上次计数值
    int16_t speed_rpm;                 // 当前转速(RPM)
    int16_t speed_buffer[ENCODER_FILTER_SIZE]; // 速度滤波缓冲区
    uint8_t buffer_index;              // 缓冲区索引
    uint32_t last_update_time;         // 上次更新时间
} Encoder_Data_t;

// 函数声明
void encoder_init(void);                    // 编码器初始化
int32_t Encoder_GetCount(uint8_t motor_id); // 获取编码器计数(motor_id: 1或2)
int16_t Encoder_GetSpeed(uint8_t motor_id); // 获取编码器速度RPM(motor_id: 1或2)

// 全局变量声明
extern int32_t count;        // 电机1计数值(兼容原代码)
extern int16_t motor1_speed; // 电机1速度RPM
extern int16_t motor2_speed; // 电机2速度RPM

#endif //ENCODER_H
