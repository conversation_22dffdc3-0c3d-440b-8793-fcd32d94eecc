#include "encoder.h"
#include "ultrasconic.h" // 包含超声波头文件，用于中断回调函数

// 原有编码器模式代码 - 已注释，不适用于非常规硬件连接
/*
static Encoder_Data_t encoder_data = {0};

void encoder_init(void) {
    HAL_TIM_Encoder_Start(&htim3, TIM_CHANNEL_3 | TIM_CHANNEL_4);
    HAL_TIM_Encoder_Start(&htim5, TIM_CHANNEL_3 | TIM_CHANNEL_4);

    __HAL_TIM_SET_COUNTER(&htim3, 32768);
    __HAL_TIM_SET_COUNTER(&htim5, 32768);
}
*/

// 新的编码器数据结构 - 适配非常规硬件连接
static Encoder_Data_t encoder1_data = {0}; // 电机1编码器数据
static Encoder_Data_t encoder2_data = {0}; // 电机2编码器数据

// 编码器状态变量
static volatile uint32_t encoder1_last_time = 0; // 电机1上次脉冲时间
static volatile uint32_t encoder2_last_time = 0; // 电机2上次脉冲时间
static volatile uint8_t encoder1_a_state = 0;    // 电机1 A相状态
static volatile uint8_t encoder2_a_state = 0;    // 电机2 A相状态

/**
 * @brief 新的编码器初始化函数 - 适配非常规硬件连接
 * 硬件连接：A1->PA2(TIM5_CH3), B1->PB0(TIM3_CH3), A2->PA3(TIM5_CH4), B2->PB1(TIM3_CH4)
 */
void encoder_init(void) {
    // 启动输入捕获模式
    HAL_TIM_IC_Start_IT(&htim3, TIM_CHANNEL_3); // B1相中断
    HAL_TIM_IC_Start_IT(&htim3, TIM_CHANNEL_4); // B2相中断
    HAL_TIM_IC_Start_IT(&htim5, TIM_CHANNEL_3); // A1相中断
    HAL_TIM_IC_Start_IT(&htim5, TIM_CHANNEL_4); // A2相中断

    // 初始化编码器数据
    encoder1_data.total_count = 0;
    encoder1_data.last_count = 0;
    encoder1_data.speed_rpm = 0;
    encoder1_data.buffer_index = 0;
    encoder1_data.last_update_time = HAL_GetTick();

    encoder2_data.total_count = 0;
    encoder2_data.last_count = 0;
    encoder2_data.speed_rpm = 0;
    encoder2_data.buffer_index = 0;
    encoder2_data.last_update_time = HAL_GetTick();
}

// 原有函数 - 已注释，不适用于新的硬件连接
/*
int32_t Encoder_GetCount(TIM_HandleTypeDef *htim) {
    int32_t current_count = (int32_t)__HAL_TIM_GET_COUNTER(htim);
    int32_t delta = current_count - encoder_data.last_count;

    // 处理定时器溢出
    if (delta > 32767) {
        delta -= 65536;
    } else if (delta < -32767) {
        delta += 65536;
    }

    encoder_data.total_count += delta;
    encoder_data.last_count = current_count;

    return encoder_data.total_count;
}
*/
/**
 * @brief 读取A相状态 - 通过GPIO直接读取
 */
static uint8_t read_encoder_a_state(uint8_t motor_id) {
    if (motor_id == 1) {
        return HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_2); // A1相 PA2
    } else {
        return HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_3); // A2相 PA3
    }
}

/**
 * @brief 计算编码器速度
 */
static void calculate_encoder_speed(Encoder_Data_t *encoder, uint32_t current_time) {
    uint32_t time_diff = current_time - encoder->last_update_time;
    if (time_diff >= ENCODER_SAMPLE_TIME) {
        // 计算RPM: (脉冲数 * 60000) / (时间差ms * 每转脉冲数 * 四倍频)
        int32_t pulse_diff = encoder->total_count - encoder->last_count;
        encoder->speed_rpm = (pulse_diff * 60000) / (time_diff * ENCODER_PPR * ENCODER_QUADRATURE);

        // 滤波处理
        encoder->speed_buffer[encoder->buffer_index] = encoder->speed_rpm;
        encoder->buffer_index = (encoder->buffer_index + 1) % ENCODER_FILTER_SIZE;

        // 计算平均值
        int32_t sum = 0;
        for (int i = 0; i < ENCODER_FILTER_SIZE; i++) {
            sum += encoder->speed_buffer[i];
        }
        encoder->speed_rpm = sum / ENCODER_FILTER_SIZE;

        encoder->last_count = encoder->total_count;
        encoder->last_update_time = current_time;
    }
}

/**
 * @brief 获取编码器计数值
 */
int32_t Encoder_GetCount(uint8_t motor_id) {
    if (motor_id == 1) {
        return encoder1_data.total_count;
    } else {
        return encoder2_data.total_count;
    }
}

/**
 * @brief 获取编码器速度(RPM)
 */
int16_t Encoder_GetSpeed(uint8_t motor_id) {
    uint32_t current_time = HAL_GetTick();
    if (motor_id == 1) {
        calculate_encoder_speed(&encoder1_data, current_time);
        return encoder1_data.speed_rpm;
    } else {
        calculate_encoder_speed(&encoder2_data, current_time);
        return encoder2_data.speed_rpm;
    }
}

// 全局变量供外部访问
int32_t count = 0;
int16_t motor1_speed = 0;
int16_t motor2_speed = 0;

/**
 * @brief 输入捕获中断回调函数 - 处理编码器B相信号和超声波ECHO信号
 */
void HAL_TIM_IC_CaptureCallback(TIM_HandleTypeDef *htim) {
    uint32_t current_time = HAL_GetTick();

    // 处理TIM3编码器中断
    if (htim->Instance == TIM3) {
        if (htim->Channel == HAL_TIM_ACTIVE_CHANNEL_3) {
            // B1相中断 - 电机1
            encoder1_a_state = read_encoder_a_state(1);
            if (encoder1_a_state) {
                encoder1_data.total_count++; // 正转
            } else {
                encoder1_data.total_count--; // 反转
            }
            encoder1_last_time = current_time;
        }
        else if (htim->Channel == HAL_TIM_ACTIVE_CHANNEL_4) {
            // B2相中断 - 电机2
            encoder2_a_state = read_encoder_a_state(2);
            if (encoder2_a_state) {
                encoder2_data.total_count++; // 正转
            } else {
                encoder2_data.total_count--; // 反转
            }
            encoder2_last_time = current_time;
        }
    }

    // 处理TIM4超声波ECHO中断 - 需要包含超声波头文件
    #ifdef ULTRASCONIC_H
    extern volatile uint32_t echo_start_time, echo_end_time;
    extern volatile uint8_t echo_state, measurement_done;

    if (htim->Instance == TIM4 && htim->Channel == HAL_TIM_ACTIVE_CHANNEL_4) {
        uint32_t capture_time = __HAL_TIM_GET_COMPARE(htim, TIM_CHANNEL_4);

        if (echo_state == 0) {
            // 检测到上升沿 - 回波开始
            echo_start_time = capture_time;
            echo_state = 1;

            // 切换到下降沿检测
            __HAL_TIM_SET_CAPTUREPOLARITY(htim, TIM_CHANNEL_4, TIM_INPUTCHANNELPOLARITY_FALLING);
        } else {
            // 检测到下降沿 - 回波结束
            echo_end_time = capture_time;
            measurement_done = 1;

            // 切换回上升沿检测，准备下次测量
            __HAL_TIM_SET_CAPTUREPOLARITY(htim, TIM_CHANNEL_4, TIM_INPUTCHANNELPOLARITY_RISING);
            echo_state = 0;
        }
    }
    #endif
}

// 原有的StartMotorTask函数已移至motor_driver.c中重新实现
// 这里保留编码器测试函数供调试使用
/*
void StartMotorTask(void *argument)
{
    encoder_init(); // 初始化编码器

    for(;;)
    {
        // 读取编码器数据
        count = Encoder_GetCount(1);           // 电机1计数
        motor1_speed = Encoder_GetSpeed(1);    // 电机1速度
        motor2_speed = Encoder_GetSpeed(2);    // 电机2速度

        osDelay(50); // 50ms周期
    }
}
*/