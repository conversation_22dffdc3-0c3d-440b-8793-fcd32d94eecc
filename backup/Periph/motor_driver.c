#include "motor_driver.h"
#include "esp.h"     // 包含WiFi状态检查函数
#include <stdlib.h>  // 包含abs函数
#include "json.h"
#include "math.h"

// 全局变量定义
Motor_Control_t motor_ctrl = {0};
Motor_PID_t motor_pid = {0};

// 机械中值校准相关变量
static float mechanical_zero_offset = 0.0f; // 机械中值偏移量
static uint8_t calibration_in_progress = 0;  // 校准进行中标志

// PWM平滑输出相关变量
typedef struct {
    float left_motor_smooth;   // 左电机平滑输出值
    float right_motor_smooth;  // 右电机平滑输出值
    float smooth_factor;       // 平滑系数 0-1
    uint32_t last_update_time; // 上次更新时间
} PWM_Smooth_t;

// 电机对称性诊断
typedef struct {
    uint32_t test_start_time;  // 测试开始时间
    float left_response_time;  // 左倾响应时间
    float right_response_time; // 右倾响应时间
    uint8_t test_active;       // 测试激活标志
    float last_roll;           // 上次roll值
} Motor_Symmetry_Test_t;

static PWM_Smooth_t pwm_smooth = {0};
static Motor_Symmetry_Test_t symmetry_test = {0};

/**
 * @brief 电机初始化函数
 * 初始化TB6612电机驱动，配置PWM和GPIO
 */
void motor_init(void) {
    // 启动TIM3 PWM输出 - 电机速度控制
    HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_1); // 左电机PWM (PC6)
    HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_2); // 右电机PWM (PC7)

    // 初始化TB6612控制引脚
    HAL_GPIO_WritePin(STBY_GPIO_Port, STBY_Pin, GPIO_PIN_SET);   // 使能TB6612
    HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_RESET); // 左电机方向1
    HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_RESET); // 左电机方向2
    HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_RESET); // 右电机方向1
    HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_RESET); // 右电机方向2

    // 初始化PWM占空比为0
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, 0); // 左电机PWM = 0
    __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_2, 0); // 右电机PWM = 0

    // 初始化电机控制结构
    motor_ctrl.left_speed = 0;
    motor_ctrl.right_speed = 0;
    motor_ctrl.enable = 1;
    motor_ctrl.wifi_cmd = WIFI_CMD_STOP;

    // 初始化PID控制器
    motor_pid_init();
}

/**
 * @brief 设置单个电机速度和方向
 * @param motor_id: 电机ID (1=左电机, 2=右电机)
 * @param speed: 速度值 (-1000~1000, 负值反转)
 */
void motor_set_speed(uint8_t motor_id, int16_t speed) {
    // 限制速度范围
    if (speed > MOTOR_SPEED_MAX) speed = MOTOR_SPEED_MAX;
    if (speed < -MOTOR_SPEED_MAX) speed = -MOTOR_SPEED_MAX;

    // 死区处理，避免低速抖动
    // if (abs(speed) < MOTOR_DEADZONE) speed = 0;

    uint16_t pwm_value = abs(speed); // PWM值为速度绝对值

    if (motor_id == 1) { // 左电机控制
        if (speed >= 0) { // 正转
            HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_SET);
            HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_RESET);
        } else { // 反转
            HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_SET);
        }
        __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, pwm_value); // 设置左电机PWM
    }
    else if (motor_id == 2) { // 右电机控制
        if (speed >= 0) { // 正转
            HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_SET);
            HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_RESET);
        } else { // 反转
            HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_SET);
        }
        __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_2, pwm_value); // 设置右电机PWM
    }
}
/**
 * @brief 双电机控制函数
 * @param left_speed: 左电机速度 (-1000~1000)
 * @param right_speed: 右电机速度 (-1000~1000)
 */
void motor_control(int16_t left_speed, int16_t right_speed) {
    if (motor_ctrl.enable) {
        motor_set_speed(1, left_speed);   // 控制左电机
        motor_set_speed(2, right_speed);  // 控制右电机
        motor_ctrl.left_speed = left_speed;
        motor_ctrl.right_speed = right_speed;
    } else {
        motor_stop(); // 如果未使能，停止电机
    }
}

/**
 * @brief 电机停止函数
 */
void motor_stop(void) {
    motor_set_speed(1, 0); // 停止左电机
    motor_set_speed(2, 0); // 停止右电机
    motor_ctrl.left_speed = 0;
    motor_ctrl.right_speed = 0;
}

/**
 * @brief 电机使能控制
 * @param enable: 1=使能, 0=禁用
 */
void motor_enable(uint8_t enable) {
    motor_ctrl.enable = enable;
    if (enable) {
        HAL_GPIO_WritePin(STBY_GPIO_Port, STBY_Pin, GPIO_PIN_SET); // 使能TB6612
    } else {
        HAL_GPIO_WritePin(STBY_GPIO_Port, STBY_Pin, GPIO_PIN_RESET); // 禁用TB6612
        motor_stop(); // 停止所有电机
    }
}

// 全局WiFi指令缓存，避免队列并发访问问题
static uint8_t global_wifi_cmd = 0;
static uint32_t global_wifi_cmd_time = 0;

/**
 * @brief 统一的WiFi指令处理器 - 解决队列并发访问问题
 * 只有这个函数从队列获取指令，然后分发给各个模块
 */
void process_global_wifi_command(void) {
    // 首先检查WiFi是否准备就绪
    if (!is_wifi_ready()) {
        return;
    }

    uint8_t wifi_cmd[64] = {0};

    // 尝试从WiFi队列获取指令，不阻塞
    if (osMessageQueueGet(WiFiCmdQueueHandle, wifi_cmd, 0, 0) == osOK) {
        // 检查是否为JSON调参数据
        if (wifi_cmd[0] == '{') {
            motor_process_wifi_pid_tuning((const char*)wifi_cmd);
            return;
        }

        // 解析指令，取第一个有效字符
        if (wifi_cmd[0] >= '0' && wifi_cmd[0] <= '9') {
            global_wifi_cmd = wifi_cmd[0];
            global_wifi_cmd_time = HAL_GetTick();

            // 根据指令类型分发处理
            if (wifi_cmd[0] >= '0' && wifi_cmd[0] <= '4') {
                // 电机控制指令，由motor_driver处理
                motor_ctrl.wifi_cmd = wifi_cmd[0];
            } else if (wifi_cmd[0] >= '5' && wifi_cmd[0] <= '7') {
                // 超声波模式指令，通知ultrasconic模块
                ultrasonic_process_mode_command(wifi_cmd[0]);
            }
        }
    }
}

/**
 * @brief 获取当前有效的WiFi指令（电机控制用）
 * @return 返回WiFi指令字符，无指令返回0
 */
uint8_t motor_get_wifi_command(void) {
    // 检查指令是否超时（500ms）
    if (global_wifi_cmd >= '1' && global_wifi_cmd <= '4') {
        if (HAL_GetTick() - global_wifi_cmd_time < 500) {
            return global_wifi_cmd;
        } else {
            global_wifi_cmd = 0; // 指令超时，清除
        }
    }
    return 0; // 无有效指令
}

/**
 * @brief 机械中值校准函数
 * 在小车倒下的角度进行校准，作为新的平衡点
 */
void motor_calibrate_mechanical_zero(void) {
    // 停止电机，确保安全
    motor_enable(0);

    calibration_in_progress = 1;
    my_printf(&huart3, "Calibration Started - Place car at balance point\r\n");

    // 等待2秒让用户调整小车位置
    osDelay(2000);

    // 采集当前角度作为机械中值
    MPU6050_Data_t mpu_data = get_mpu_data();
    mechanical_zero_offset = mpu_data.roll; // 使用roll角作为机械中值

    // 清除PID积分项
    motor_pid.balance_pid.errorInt = 0.0f;
    motor_pid.velocity_pid.errorInt = 0.0f;
    motor_pid.turn_pid.errorInt = 0.0f;

    calibration_in_progress = 0;
    my_printf(&huart3, "Calibration Complete: Zero=%.2f degrees\r\n", mechanical_zero_offset);

    // 重新启用电机
    motor_enable(1);
}

/**
 * @brief 获取机械中值偏移量
 * @return 机械中值偏移量（度）
 */
float motor_get_mechanical_zero(void) {
    return mechanical_zero_offset;
}

/**
 * @brief PWM平滑输出初始化
 */
void pwm_smooth_init(void) {
    pwm_smooth.left_motor_smooth = 0.0f;
    pwm_smooth.right_motor_smooth = 0.0f;
    pwm_smooth.smooth_factor = 0.7f; // 平滑系数，0.7表示70%保留上次值，30%使用新值
    pwm_smooth.last_update_time = HAL_GetTick();
}

/**
 * @brief 电机对称性诊断函数（简化版，减少串口输出）
 * 检测左右倾倒时的响应差异
 * @param current_roll: 当前roll角度
 * @param motor_output: 当前电机输出
 */
void motor_symmetry_diagnosis(float current_roll, float motor_output) {
    static uint32_t last_print_time = 0;
    static uint32_t diagnosis_count = 0;
    uint32_t current_time = HAL_GetTick();

    diagnosis_count++;

    // 只在特殊情况下输出，大幅减少串口负载
    if (fabsf(current_roll) > 10.0f) { // 只有大角度倾斜才输出
        if (current_time - last_print_time > 2000) { // 至少2秒间隔
            my_printf(&huart1, "Tilt: R=%.1f O=%.0f\r\n", current_roll, motor_output);
            last_print_time = current_time;
        }
    }

    // 每10秒输出一次简化诊断信息
    if (diagnosis_count % 833 == 0) { // 833 * 12ms ≈ 10秒
        my_printf(&huart1, "OK: R=%.1f\r\n", current_roll);
    }

    symmetry_test.last_roll = current_roll;
}

/**
 * @brief 平滑电机控制函数
 * 解决高频控制导致的电机抖动问题，并确保左右对称
 * @param left_target: 左电机目标值
 * @param right_target: 右电机目标值
 */
void motor_control_smooth(int16_t left_target, int16_t right_target) {
    uint32_t current_time = HAL_GetTick();
    float dt = (current_time - pwm_smooth.last_update_time) / 1000.0f; // 时间间隔(秒)
    pwm_smooth.last_update_time = current_time;

    // 限制dt范围
    if (dt > 0.05f) dt = 0.012f; // 最大50ms，默认12ms
    if (dt < 0.005f) dt = 0.012f; // 最小5ms，默认12ms

    // 根据变化幅度自适应调整平滑系数
    float left_delta = fabsf((float)left_target - pwm_smooth.left_motor_smooth);
    float right_delta = fabsf((float)right_target - pwm_smooth.right_motor_smooth);

    // 变化大时减少平滑，变化小时增加平滑（左右使用相同参数确保对称）
    float alpha = ((left_delta + right_delta) / 2.0f > 100.0f) ? 0.6f : 0.8f;

    // 应用平滑滤波（左右使用相同的平滑参数）
    pwm_smooth.left_motor_smooth = alpha * pwm_smooth.left_motor_smooth +
                                   (1.0f - alpha) * (float)left_target;
    pwm_smooth.right_motor_smooth = alpha * pwm_smooth.right_motor_smooth +
                                    (1.0f - alpha) * (float)right_target;

    // 输出到电机（确保左右对称，检查是否需要调整方向）
    motor_control((int16_t)pwm_smooth.left_motor_smooth, (int16_t)pwm_smooth.right_motor_smooth);
}

/**
 * @brief WiFi调参处理函数
 * @param json_data: JSON格式的PID参数数据
 */
void motor_process_wifi_pid_tuning(const char* json_data) {
    PID_Params_t params;

    if (json_parse_pid_params(json_data, &params) && params.valid) {
        // 更新平衡PID参数
        motor_pid.balance_pid.Kp = params.kp;
        motor_pid.balance_pid.Ki = params.ki;
        motor_pid.balance_pid.Kd = params.kd;

        // 清除积分项，避免参数切换时的积分饱和
        motor_pid.balance_pid.errorInt = 0.0f;

        // 通过UART1发送确认信息
        my_printf(&huart3, "PID Updated: Kp=%.2f Ki=%.2f Kd=%.2f\r\n",
                  params.kp, params.ki, params.kd);
    } else {
        my_printf(&huart3, "JSON Parse Error: %s\r\n", json_data);
    }
}

/**
 * @brief 根据WiFi指令计算目标速度
 * @param cmd: WiFi指令字符
 * @param left_speed: 输出左电机目标速度
 * @param right_speed: 输出右电机目标速度
 */
void motor_process_wifi_command(uint8_t cmd, int16_t *left_speed, int16_t *right_speed) {
    const int16_t base_speed = 300; // 基础速度
    const int16_t turn_speed = 200; // 转向速度差

    switch (cmd) {
        case WIFI_CMD_FORWARD:  // 前进
            *left_speed = base_speed;
            *right_speed = base_speed;
            break;

        case WIFI_CMD_BACKWARD: // 后退
            *left_speed = -base_speed;
            *right_speed = -base_speed;
            break;

        case WIFI_CMD_LEFT:     // 左转
            *left_speed = base_speed - turn_speed;
            *right_speed = base_speed + turn_speed;
            break;

        case WIFI_CMD_RIGHT:    // 右转
            *left_speed = base_speed + turn_speed;
            *right_speed = base_speed - turn_speed;
            break;

        case WIFI_CMD_STOP:     // 停止
        default:
            *left_speed = 0;
            *right_speed = 0;
            break;
    }
}

/**
 * @brief PID控制器初始化
 */
void motor_pid_init(void) {
    // 平衡PID参数初始化 - 针对高频控制优化
    motor_pid.balance_pid.Kp = 15.0f;     // 比例系数，进一步降低避免高频震荡
    motor_pid.balance_pid.Ki = 0.0f;      // 积分系数，先设为0
    motor_pid.balance_pid.Kd = 8.0f;      // 微分系数，降低避免高频噪声放大
    motor_pid.balance_pid.maximum = 800.0f;   // 输出限幅，进一步降低避免过激
    motor_pid.balance_pid.minimum = -800.0f;
    motor_pid.balance_pid.target = 0.0f;     // 目标角度为0度（直立）
    motor_pid.balance_pid.error0 = 0.0f;
    motor_pid.balance_pid.error1 = 0.0f;
    motor_pid.balance_pid.errorInt = 0.0f;

    // 速度PID参数初始化 - 控制前进后退速度
    // motor_pid.velocity_pid.Kp = 0.8f;     // 比例系数，建议范围：0.5-2.0
    // motor_pid.velocity_pid.Ki = 0.02f;    // 积分系数，建议范围：0-0.1
    // motor_pid.velocity_pid.Kd = 0.1f;     // 微分系数，建议范围：0-0.5
    motor_pid.velocity_pid.Kp = 0.0f;     // 比例系数，建议范围：0.5-2.0
    motor_pid.velocity_pid.Ki = 0.0f;    // 积分系数，建议范围：0-0.1
    motor_pid.velocity_pid.Kd = 0.0f;     // 微分系数，建议范围：0-0.5
    motor_pid.velocity_pid.maximum = 15.0f;  // 输出限幅（角度偏移）
    motor_pid.velocity_pid.minimum = -15.0f;
    motor_pid.velocity_pid.target = 0.0f;    // 目标速度
    motor_pid.velocity_pid.error0 = 0.0f;
    motor_pid.velocity_pid.error1 = 0.0f;
    motor_pid.velocity_pid.errorInt = 0.0f;

    // 转向PID参数初始化 - 控制左右转向
    // motor_pid.turn_pid.Kp = 2.0f;         // 比例系数，建议范围：1-5
    // motor_pid.turn_pid.Ki = 0.0f;         // 积分系数，建议范围：0-0.5
    // motor_pid.turn_pid.Kd = 0.5f;         // 微分系数，建议范围：0-2
    motor_pid.turn_pid.Kp = 0.0f;         // 比例系数，建议范围：1-5
    motor_pid.turn_pid.Ki = 0.0f;         // 积分系数，建议范围：0-0.5
    motor_pid.turn_pid.Kd = 0.0f;         // 微分系数，建议范围：0-2
    motor_pid.turn_pid.maximum = 800.0f;     // 输出限幅
    motor_pid.turn_pid.minimum = -800.0f;
    motor_pid.turn_pid.target = 0.0f;        // 目标转向角速度
    motor_pid.turn_pid.error0 = 0.0f;
    motor_pid.turn_pid.error1 = 0.0f;
    motor_pid.turn_pid.errorInt = 0.0f;
}

/**
 * @brief PID控制计算函数
 * @param pitch: 当前俯仰角（度）
 * @param target_velocity: 目标速度
 * @param turn_rate: 目标转向角速度
 */
void motor_pid_calculate(float roll, float target_velocity, float turn_rate) {
    // 1.速度PID计算 - 基于编码器速度反馈（外环）
    float current_velocity = (Encoder_GetSpeed(1) + Encoder_GetSpeed(2)) / 2.0f; // 平均速度

    // 2. 速度PID计算 - 基于编码器速度反馈（外环）
    motor_pid.velocity_pid.actual = current_velocity;
    motor_pid.velocity_pid.target = target_velocity;
    pid_update(&motor_pid.velocity_pid);

    // 3. 平衡PID计算 - 基于roll角（内环）
    motor_pid.balance_pid.actual = roll - mechanical_zero_offset; // 减去机械中值偏移
    motor_pid.balance_pid.target = motor_pid.velocity_pid.output; // 速度PID输出作为平衡角度偏移
    pid_update(&motor_pid.balance_pid);

    // 4. 转向PID计算 - 基于陀螺仪角速度
    motor_pid.turn_pid.actual = 0.0f; // 这里应该是陀螺仪Z轴角速度，暂时设为0
    motor_pid.turn_pid.target = turn_rate;
    pid_update(&motor_pid.turn_pid);

    // 5. 计算基础电机输出
    float balance_output = motor_pid.balance_pid.output;  // 平衡控制输出
    float turn_output = motor_pid.turn_pid.output;       // 转向控制输出

    // 6. 分配到左右电机并应用校正
    float left_raw = balance_output - turn_output;
    float right_raw = balance_output + turn_output;

    // 7. 在这里进行死区处理，并给一个最小启动值
    float left_final = left_raw;
    float right_final = right_raw;

    // 定义小角度阈值和补偿PWM
    const float ANGLE_THRESHOLD = 2.1f;

    float current_roll_error = roll - mechanical_zero_offset;

    // 如果角度在小范围内，并且PID输出非零
    if (fabsf(current_roll_error) > 0.5f && fabsf(current_roll_error) <= ANGLE_THRESHOLD) {
        // 在PID输出较小时，给一个补偿值，使其能越过摩擦力死区
        if (left_final > 0) {
            // 如果PID输出小于补偿值，就用PID输出加上补偿值
            if (left_final < PWM_COMPENSATION) {
                left_final = left_final * 1.38f + PWM_COMPENSATION;
            }
        } else if (left_final < 0) {
            // 同理，如果PID输出大于-补偿值，就用PID输出减去补偿值
            if (left_final > -PWM_COMPENSATION) {
                left_final = left_final * 1.38f - PWM_COMPENSATION;
            }
        }

        // ... 对右电机做同样处理
        if (right_final > 0) {
            if (right_final < PWM_COMPENSATION) {
                right_final = right_final * 1.38f + PWM_COMPENSATION;
            }
        } else if (right_final < 0) {
            if (right_final > -PWM_COMPENSATION) {
                right_final = right_final * 1.38f - PWM_COMPENSATION;
            }
        }
    }
    // 如果角度超过阈值，不进行补偿，使用正常的PID输出

    // 8. 输出到电机
    motor_control((int16_t)left_final, -(int16_t)right_final); // 注意你的右电机输出是反向的
}

/**
 * @brief 倾斜角度>40度时停止电机
 * @param pitch: 当前俯仰角（度）
 */
void motor_stop_roll(float roll) {
    if (fabsf(roll) > 40.0f) {
        motor_ctrl.enable = 0; // 停止电机
    }
    else {
        motor_ctrl.enable = 1; // 启动电机
    }
}

/**
 * @brief 电机控制主任务 - 覆盖原有的弱函数
 * 集成WiFi遥控、PID平衡控制、编码器反馈
 */
void StartMotorTask(void *argument) {

    // 初始化电机系统
    motor_init();
    encoder_init(); // 确保编码器已初始化
    pwm_smooth_init(); // 初始化PWM平滑输出

    // 等待WiFi初始化完成
    uint32_t wifi_wait_start = HAL_GetTick();
    const uint32_t wifi_wait_timeout = 20000; // 最多等待30秒
    uint8_t wifi_ready_logged = 0;

    while (!is_wifi_ready() && (HAL_GetTick() - wifi_wait_start) < wifi_wait_timeout) {
        if (!wifi_ready_logged) {
            // 只打印一次等待信息，避免刷屏
            wifi_ready_logged = 1;
        }
        osDelay(1000); // 每秒检查一次WiFi状态
    }

    // 任务变量
    uint8_t wifi_cmd = 0;
    int16_t target_left = 0, target_right = 0;
    float target_velocity = 0.0f, turn_rate = 0.0f;
    MPU6050_Data_t mpu_data = {0};
    uint32_t last_wifi_time = 0;
    const uint32_t wifi_timeout = 500; // WiFi指令超时时间(ms)
    uint8_t wifi_status_logged = 0;

    // 性能监控变量
    uint32_t loop_count = 0;
    uint32_t last_performance_time = HAL_GetTick();

    for(;;) {
        uint32_t current_time = HAL_GetTick();

        // 1. 统一处理WiFi指令（避免队列并发访问）
        process_global_wifi_command();

        // 2. 获取电机控制指令
        if (is_wifi_ready()) {
            if (!wifi_status_logged) {
                wifi_status_logged = 1; // 只记录一次WiFi就绪状态
            }
            wifi_cmd = motor_get_wifi_command();
        } else {
            wifi_cmd = 0; // WiFi未就绪时清除指令
            wifi_status_logged = 0; // 重置日志标志
        }
        if (wifi_cmd != 0) {
            last_wifi_time = current_time; // 更新最后接收时间

            // 根据WiFi指令设置目标参数
            switch (wifi_cmd) {
                case WIFI_CMD_FORWARD:
                    target_velocity = 50.0f;  // 前进速度
                    turn_rate = 0.0f;
                    break;
                case WIFI_CMD_BACKWARD:
                    target_velocity = -50.0f; // 后退速度
                    turn_rate = 0.0f;
                    break;
                case WIFI_CMD_LEFT:
                    target_velocity = 0.0f;
                    turn_rate = -30.0f;       // 左转角速度
                    break;
                case WIFI_CMD_RIGHT:
                    target_velocity = 0.0f;
                    turn_rate = 30.0f;        // 右转角速度
                    break;
                case WIFI_CMD_STOP:
                    target_velocity = 0.0f;
                    turn_rate = 0.0f;
                    break;
                default:
                    target_velocity = 0.0f;
                    turn_rate = 0.0f;
                    break;
            }
        }

        // // 2. WiFi指令超时检查
        // if (current_time - last_wifi_time > wifi_timeout) {
        //     target_velocity = 0.0f; // 超时则停止
        //     turn_rate = 0.0f;
        // }

        // 3. 获取MPU6050传感器数据 - 使用阻塞方式确保数据连续性
        static MPU6050_Data_t last_mpu_data = {0}; // 保存上次数据
        static uint8_t sensor_data_valid = 0;

        // 尝试获取新的传感器数据，如果没有则使用上次数据
        if (osMessageQueueGet(SensorDataQueueHandle, &mpu_data, 0, 0) == osOK) {
            last_mpu_data = mpu_data; // 保存最新数据
            sensor_data_valid = 1;
        } else if (sensor_data_valid) {
            mpu_data = last_mpu_data; // 使用上次数据，保持控制连续性
        }

        // 4. 只有在有有效传感器数据时才进行PID控制
        if (sensor_data_valid) {
            motor_pid_calculate(mpu_data.roll, target_velocity, turn_rate);
            motor_stop_roll(mpu_data.roll);
            // 减少串口输出频率，避免阻塞 - 每100次输出一次
            static uint32_t debug_counter = 0;
            if (++debug_counter >= 100) {
                debug_counter = 0;
                int target = (int)(motor_pid.balance_pid.target * 100);
                int actual = (int)(motor_pid.balance_pid.actual * 100);
                int out = (int)(motor_pid.balance_pid.output);
                my_printf(&huart1, "PID: T=%d A=%d O=%d\r\n", target, actual, out);
            }
        } else {
            // 传感器数据无效时停止电机，避免危险
            motor_stop();
        }

        // 8. 性能监控 - 每5秒输出一次性能信息
        loop_count++;
        if (current_time - last_performance_time >= 5000) {
            uint32_t avg_loop_time = 5000 / loop_count;
            my_printf(&huart1, "Motor: %lu loops, %lums/loop\r\n", loop_count, avg_loop_time);
            loop_count = 0;
            last_performance_time = current_time;
        }

        // 9. 固定10ms延时，与传感器任务同步
        osDelay(10);
    }
}

/*
 * 代码运行逻辑详解：
 *
 * 1. 初始化阶段：
 *    - motor_init(): 配置TB6612驱动、PWM输出、GPIO控制
 *    - encoder_init(): 启动编码器输入捕获中断
 *    - motor_pid_init(): 初始化三个PID控制器参数
 *
 * 2. 主循环运行逻辑：
 *    a) WiFi指令处理：
 *       - 从WiFiCmdQueueHandle队列获取遥控指令
 *       - 解析指令(1前进/2后退/3左转/4右转/0停止)
 *       - 转换为目标速度和转向角速度
 *       - 实现超时保护，500ms无指令自动停止
 *
 *    b) 传感器数据获取：
 *       - 从SensorDataQueueHandle获取MPU6050数据
 *       - 提取俯仰角(pitch)用于平衡控制
 *
 *    c) PID控制计算：
 *       - 平衡PID：基于俯仰角，输出平衡力矩
 *       - 速度PID：基于编码器反馈，控制前进后退
 *       - 转向PID：基于目标转向角速度，控制左右转向
 *       - 三环PID串级控制，确保平衡车稳定运行
 *
 *    d) 电机输出：
 *       - 将PID输出分配到左右电机
 *       - 通过PWM控制电机速度
 *       - 通过GPIO控制电机方向
 *
 * 3. 安全保护机制：
 *    - 电机死区处理，避免低速抖动
 *    - PWM输出限幅，防止过载
 *    - WiFi超时保护，通信中断自动停止
 *    - PID输出限幅，防止积分饱和
 *
 * 4. 控制周期：20ms (50Hz)，确保实时性和稳定性
 */
