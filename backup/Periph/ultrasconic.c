#include "ultrasconic.h"
#include "esp.h"     // 包含WiFi状态检查函数
#include <stdlib.h>  // 包含abs函数

// 全局变量定义
Ultrasonic_Data_t ultrasonic_data = {0};

// 全局变量 - 用于输入捕获测量（供中断回调函数访问）
volatile uint32_t echo_start_time = 0;   // 回波开始时间
volatile uint32_t echo_end_time = 0;     // 回波结束时间
volatile uint8_t echo_state = 0;         // 回波状态：0=等待上升沿，1=等待下降沿
volatile uint8_t measurement_done = 0;   // 测量完成标志

/**
 * @brief 超声波传感器初始化
 */
void ultrasonic_init(void) {
    // 启动TIM4输入捕获中断 - ECHO信号检测
    HAL_TIM_IC_Start_IT(&htim4, TIM_CHANNEL_4);

    // 初始化TRIG引脚为低电平
    HAL_GPIO_WritePin(TRIG_GPIO_Port, TRIG_Pin, GPIO_PIN_RESET);

    // 初始化超声波数据结构
    ultrasonic_data.distance = 0.0f;
    ultrasonic_data.buffer_index = 0;
    ultrasonic_data.last_measure_time = 0;
    ultrasonic_data.mode = ULTRASONIC_MODE_OFF;
    ultrasonic_data.measurement_valid = 0;

    // 清空滤波缓冲区
    for (int i = 0; i < 5; i++) {
        ultrasonic_data.distance_buffer[i] = 0.0f;
    }

    // 初始化静态变量
    echo_start_time = 0;
    echo_end_time = 0;
    echo_state = 0;
    measurement_done = 0;
}

/**
 * @brief 发送超声波触发脉冲
 * HCSR04需要至少10μs的高电平触发脉冲
 */
void ultrasonic_trigger(void) {
    // 发送10μs高电平脉冲
    HAL_GPIO_WritePin(TRIG_GPIO_Port, TRIG_Pin, GPIO_PIN_SET);
    HAL_Delay(1); // 延时1ms，确保脉冲宽度足够
    HAL_GPIO_WritePin(TRIG_GPIO_Port, TRIG_Pin, GPIO_PIN_RESET);

    // 重置测量状态
    echo_state = 0;
    measurement_done = 0;
    echo_start_time = 0;
    echo_end_time = 0;
}

/**
 * @brief 根据时间差计算距离
 * @param time_us: 回波时间(微秒)
 * @return 距离值(cm)
 */
float ultrasonic_calculate_distance(uint32_t time_us) {
    // 距离 = (时间 × 声速) / 2
    // 声速340m/s = 0.034cm/μs
    float distance = (float)time_us * 0.034f / 2.0f;

    // 限制测距范围
    if (distance < ULTRASONIC_MIN_DISTANCE) {
        distance = ULTRASONIC_MIN_DISTANCE;
    } else if (distance > ULTRASONIC_MAX_DISTANCE) {
        distance = ULTRASONIC_MAX_DISTANCE;
    }

    return distance;
}
/**
 * @brief 超声波测距主函数
 * @return 滤波后的距离值(cm)，测量失败返回-1
 */
float ultrasonic_measure(void) {
    uint32_t timeout_start = HAL_GetTick();
    const uint32_t timeout_ms = 100; // 100ms超时

    // 发送触发脉冲
    ultrasonic_trigger();

    // 等待测量完成或超时
    while (!measurement_done && (HAL_GetTick() - timeout_start) < timeout_ms) {
        osDelay(1); // 让出CPU时间
    }

    if (!measurement_done) {
        return -1.0f; // 测量超时
    }

    // 计算回波时间
    uint32_t echo_time = echo_end_time - echo_start_time;

    // 检查时间合理性
    if (echo_time > ULTRASONIC_TIMEOUT || echo_time < 100) {
        return -1.0f; // 时间异常
    }

    // 计算距离
    float distance = ultrasonic_calculate_distance(echo_time);

    // 滤波处理
    ultrasonic_data.distance_buffer[ultrasonic_data.buffer_index] = distance;
    ultrasonic_data.buffer_index = (ultrasonic_data.buffer_index + 1) % 5;

    // 计算平均值
    float sum = 0.0f;
    for (int i = 0; i < 5; i++) {
        sum += ultrasonic_data.distance_buffer[i];
    }
    float filtered_distance = sum / 5.0f;

    ultrasonic_data.distance = filtered_distance;
    ultrasonic_data.measurement_valid = 1;
    ultrasonic_data.last_measure_time = HAL_GetTick();

    return filtered_distance;
}

// TIM4输入捕获中断回调函数已合并到encoder.c中的HAL_TIM_IC_CaptureCallback函数
// 该函数现在同时处理编码器和超声波的输入捕获中断

/**
 * @brief 处理WiFi模式指令
 * @param cmd: WiFi指令字符
 */
void ultrasonic_process_mode_command(uint8_t cmd) {
    switch (cmd) {
        case WIFI_CMD_AVOID_ON:     // 指令5 - 开启避障
            ultrasonic_data.mode = ULTRASONIC_MODE_AVOID;
            break;

        case WIFI_CMD_FOLLOW_ON:    // 指令6 - 开启跟随
            ultrasonic_data.mode = ULTRASONIC_MODE_FOLLOW;
            break;

        case WIFI_CMD_ALL_OFF:      // 指令7 - 关闭所有
            ultrasonic_data.mode = ULTRASONIC_MODE_OFF;
            break;

        default:
            // 其他指令不改变模式
            break;
    }
}
/**
 * @brief 避障逻辑实现
 * @param distance: 当前测距值(cm)
 */
void ultrasonic_obstacle_avoidance(float distance) {
    static uint32_t turn_start_time = 0;
    static uint8_t turning = 0;
    const uint32_t turn_duration = 1000; // 转向持续时间1秒

    if (distance > 0 && distance < OBSTACLE_THRESHOLD) {
        // 检测到障碍物
        if (!turning) {
            // 开始转向避障
            motor_control(-TURN_SPEED, TURN_SPEED); // 左转避障
            turn_start_time = HAL_GetTick();
            turning = 1;
        } else {
            // 检查转向时间
            if (HAL_GetTick() - turn_start_time > turn_duration) {
                turning = 0; // 转向完成
            }
        }
    } else if (!turning) {
        // 无障碍物且未在转向，可以前进
        motor_control(300, 300); // 缓慢前进
    }

    // 如果正在转向，保持转向状态直到时间到
    if (turning && (HAL_GetTick() - turn_start_time <= turn_duration)) {
        motor_control(-TURN_SPEED, TURN_SPEED); // 继续左转
    }
}

/**
 * @brief 跟随逻辑实现
 * @param distance: 当前测距值(cm)
 */
void ultrasonic_target_following(float distance) {
    if (distance <= 0) {
        // 测距无效，停止
        motor_control(0, 0);
        return;
    }

    float distance_error = distance - FOLLOW_TARGET_DISTANCE;

    if (abs((int)distance_error) <= FOLLOW_TOLERANCE) {
        // 距离合适，保持静止
        motor_control(0, 0);
    } else if (distance_error > FOLLOW_TOLERANCE) {
        // 目标太远，前进跟随
        int16_t speed = (int16_t)(distance_error * 10); // 比例控制
        if (speed > 400) speed = 400; // 限制最大速度
        motor_control(speed, speed);
    } else if (distance_error < -FOLLOW_TOLERANCE) {
        // 目标太近，后退
        int16_t speed = (int16_t)(distance_error * 10); // 比例控制
        if (speed < -300) speed = -300; // 限制最大后退速度
        motor_control(speed, speed);
    }
}

/**
 * @brief 获取当前工作模式
 * @return 当前超声波工作模式
 */
Ultrasonic_Mode_t ultrasonic_get_mode(void) {
    return ultrasonic_data.mode;
}

/**
 * @brief 获取当前测距值
 * @return 当前测距值(cm)，若测距无效返回-1.0f
 */
float ultrasonic_get_distance(void) {
    return ultrasonic_data.distance;
}

/**
 * @brief 超声波主任务函数 - 覆盖原有的弱函数
 */
void StartUltrasonicTask(void *argument) {
    // 初始化超声波传感器
    ultrasonic_init();

    uint8_t wifi_cmd = 0;
    float distance = 0.0f;
    uint32_t last_measure_time = 0;
    const uint32_t measure_interval = 100; // 100ms测量间隔

    for(;;) {
        uint32_t current_time = HAL_GetTick();

        // 1. WiFi模式指令由motor_driver中的统一指令处理器处理
        // 这里不再直接访问WiFi队列，避免并发访问问题

        // 2. 定时进行超声波测距
        if (current_time - last_measure_time >= measure_interval) {
            distance = ultrasonic_measure();
            last_measure_time = current_time;

        }

        // 3. 根据当前模式执行相应逻辑
        switch (ultrasonic_data.mode) {
            case ULTRASONIC_MODE_AVOID:
                if (distance > 0) {
                    ultrasonic_obstacle_avoidance(distance);
                }
                break;

            case ULTRASONIC_MODE_FOLLOW:
                if (distance > 0) {
                    ultrasonic_target_following(distance);
                }
                break;

            case ULTRASONIC_MODE_OFF:
            default:
                // 关闭模式，不进行任何控制
                break;
        }

        // 4. 任务延时 - 控制周期为50ms
        osDelay(50);
    }
}
