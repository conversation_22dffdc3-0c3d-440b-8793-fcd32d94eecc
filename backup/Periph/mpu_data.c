#include "mpu_data.h"
#include <math.h>

MPU6050_State_t g_mpu_state = {0};
MPU6050_Data_t mpu_data;
MPU6050_Data_t filtered_mpu_data; // 滤波后的数据
Advanced_Filter_t advanced_filter; // 高级滤波器

MPU6050_Data_t get_mpu_data(void) {
    return filtered_mpu_data; // 返回滤波后的数据
}

/**
 * @brief 卡尔曼滤波器初始化
 * @param kalman: 卡尔曼滤波器结构体指针
 */
void kalman_init(Kalman_t *kalman) {
    kalman->Q_angle = 0.001f;   // 过程噪声协方差 - 角度
    kalman->Q_bias = 0.003f;    // 过程噪声协方差 - 偏差
    kalman->R_measure = 0.03f;  // 测量噪声协方差
    kalman->angle = 0.0f;       // 初始角度
    kalman->bias = 0.0f;        // 初始偏差
    kalman->rate = 0.0f;        // 初始角速度

    // 初始化误差协方差矩阵
    kalman->P[0][0] = 0.0f;
    kalman->P[0][1] = 0.0f;
    kalman->P[1][0] = 0.0f;
    kalman->P[1][1] = 0.0f;
}

/**
 * @brief 自适应滤波器初始化
 * @param filter: 自适应滤波器结构体指针
 */
void adaptive_filter_init(Adaptive_Filter_t *filter) {
    filter->alpha = 0.1f;       // 初始滤波系数
    filter->prev_value = 0.0f;  // 上一次值
    filter->variance = 0.0f;    // 方差
    filter->init = 0;           // 未初始化
}

/**
 * @brief 简化滤波器初始化
 */
void advanced_filter_init(void) {
    // 初始化滤波后的数据为0
    filtered_mpu_data.pitch = 0.0f;
    filtered_mpu_data.roll = 0.0f;
    filtered_mpu_data.yaw = 0.0f;
}

/**
 * @brief 卡尔曼滤波器
 * @param kalman: 卡尔曼滤波器结构体指针
 * @param newAngle: 新的角度测量值（来自加速度计）
 * @param newRate: 新的角速度测量值（来自陀螺仪）
 * @param dt: 时间间隔
 * @return 滤波后的角度
 */
float kalman_filter(Kalman_t *kalman, float newAngle, float newRate, float dt) {
    // 预测步骤
    kalman->rate = newRate - kalman->bias;
    kalman->angle += dt * kalman->rate;

    // 更新误差协方差矩阵
    kalman->P[0][0] += dt * (dt * kalman->P[1][1] - kalman->P[0][1] - kalman->P[1][0] + kalman->Q_angle);
    kalman->P[0][1] -= dt * kalman->P[1][1];
    kalman->P[1][0] -= dt * kalman->P[1][1];
    kalman->P[1][1] += kalman->Q_bias * dt;

    // 计算卡尔曼增益
    float S = kalman->P[0][0] + kalman->R_measure;
    float K[2];
    K[0] = kalman->P[0][0] / S;
    K[1] = kalman->P[1][0] / S;

    // 更新估计
    float y = newAngle - kalman->angle;
    kalman->angle += K[0] * y;
    kalman->bias += K[1] * y;

    // 更新误差协方差矩阵
    float P00_temp = kalman->P[0][0];
    float P01_temp = kalman->P[0][1];

    kalman->P[0][0] -= K[0] * P00_temp;
    kalman->P[0][1] -= K[0] * P01_temp;
    kalman->P[1][0] -= K[1] * P00_temp;
    kalman->P[1][1] -= K[1] * P01_temp;

    return kalman->angle;
}

/**
 * @brief 自适应滤波器
 * @param filter: 自适应滤波器结构体指针
 * @param input: 输入值
 * @return 滤波后的值
 */
float adaptive_filter(Adaptive_Filter_t *filter, float input) {
    if (!filter->init) {
        filter->prev_value = input;
        filter->init = 1;
        return input;
    }

    // 计算变化率
    float delta = fabsf(input - filter->prev_value);

    // 自适应调整滤波系数
    if (delta > 2.0f) {
        filter->alpha = 0.8f; // 快速变化时，增加响应速度
    } else if (delta > 0.5f) {
        filter->alpha = 0.3f; // 中等变化
    } else {
        filter->alpha = 0.1f; // 缓慢变化时，增加平滑度
    }

    // 一阶低通滤波
    float output = filter->alpha * input + (1.0f - filter->alpha) * filter->prev_value;
    filter->prev_value = output;

    return output;
}

/**
 * @brief 互补滤波器
 * @param accel_angle: 加速度计角度
 * @param gyro_rate: 陀螺仪角速度
 * @param dt: 时间间隔
 * @param alpha: 滤波系数
 * @return 滤波后的角度
 */
float complementary_filter(float accel_angle, float gyro_rate, float dt, float alpha) {
    static float angle = 0.0f;

    // 互补滤波：alpha * 陀螺仪积分 + (1-alpha) * 加速度计
    angle = alpha * (angle + gyro_rate * dt) + (1.0f - alpha) * accel_angle;

    return angle;
}

/**
 * @brief 滤波器性能测试函数
 * 输出滤波前后的数据对比
 */
void filter_performance_test(void) {
    static uint32_t test_count = 0;
    test_count++;

    // 每5秒输出一次对比数据
    if (test_count % 500 == 0) { // 500 * 10ms = 5秒
        my_printf(&huart1, "Filter Test - Raw: P=%.2f R=%.2f | Filtered: P=%.2f R=%.2f\r\n",
                  mpu_data.pitch, mpu_data.roll,
                  filtered_mpu_data.pitch, filtered_mpu_data.roll);
    }
}
void StartSensorTask(void *argument)
{
    do {
        osDelay(1000);
        g_mpu_state.g_mpu_state = MPU6050_DMP_init();
        my_printf(&huart1, "MPU6050 Init State: %d\r\nSelf Test: %d\r\n", g_mpu_state.g_mpu_state,mpu_self_test_result);
        if (g_mpu_state.g_mpu_state !=0) g_mpu_state.count++;
    }while(g_mpu_state.g_mpu_state);

    g_mpu_state.count=0;
    g_mpu_state.g_mpu_state=0;

    // 初始化简化滤波器
    advanced_filter_init();
    my_printf(&huart1, "Simple Filter Initialized\r\n");

    uint8_t first_data = 1; // 第一次数据标志

    for(;;)
    {
        if (MPU6050_DMP_Get_Date(&mpu_data.pitch, &mpu_data.roll, &mpu_data.yaw)==0) {
            // 第一次获取数据时，直接赋值，不进行滤波
            if (first_data) {
                filtered_mpu_data.pitch = mpu_data.pitch;
                filtered_mpu_data.roll = mpu_data.roll;
                filtered_mpu_data.yaw = mpu_data.yaw;
                first_data = 0;
                my_printf(&huart1, "Filter initialized with: P=%.2f R=%.2f Y=%.2f\r\n",
                          mpu_data.pitch, mpu_data.roll, mpu_data.yaw);
            } else {

            // 简化滤波：只使用一阶低通滤波，专为平衡车优化
            const float alpha = 0.85f; // 滤波系数，85%保留原值，15%新值

            // 静止检测：如果变化很小，认为是静止状态，加强滤波
            float pitch_delta = fabsf(mpu_data.pitch - filtered_mpu_data.pitch);
            float roll_delta = fabsf(mpu_data.roll - filtered_mpu_data.roll);

            float pitch_alpha = alpha;
            float roll_alpha = alpha;

            // 静止时加强滤波，减少漂移
            if (pitch_delta < 0.1f) pitch_alpha = 0.95f; // 变化小于0.1度时强滤波
            if (roll_delta < 0.1f) roll_alpha = 0.95f;   // 变化小于0.1度时强滤波

                // 应用滤波
                filtered_mpu_data.pitch = pitch_alpha * filtered_mpu_data.pitch + (1.0f - pitch_alpha) * mpu_data.pitch;
                filtered_mpu_data.roll = roll_alpha * filtered_mpu_data.roll + (1.0f - roll_alpha) * mpu_data.roll;
                filtered_mpu_data.yaw = mpu_data.yaw; // Yaw直接使用DMP输出
            }

            // 发送滤波后的数据 - 确保队列不满，覆盖旧数据
            if (osMessageQueuePut(SensorDataQueueHandle, &filtered_mpu_data, 0, 0) != osOK) {
                // 队列满时，清空队列并重新发送，确保最新数据
                osMessageQueueReset(SensorDataQueueHandle);
                osMessageQueuePut(SensorDataQueueHandle, &filtered_mpu_data, 0, 0);
            }

            // 性能测试（可选，用于调试）
            // filter_performance_test();
        }
        osDelay(10);
    }
}
