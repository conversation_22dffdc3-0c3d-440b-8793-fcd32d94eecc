#include "json.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>

/**
 * @brief 查找JSON字段值
 * @param json_str: JSON字符串
 * @param key: 要查找的键名
 * @param value: 输出值字符串
 * @param max_len: 值字符串最大长度
 * @return 1=成功, 0=失败
 */
static uint8_t json_find_value(const char* json_str, const char* key, char* value, uint8_t max_len) {
    char search_key[32];
    snprintf(search_key, sizeof(search_key), "\"%s\":", key); // 构造搜索模式 "key":
    
    const char* key_pos = strstr(json_str, search_key);
    if (!key_pos) return 0; // 未找到键
    
    key_pos += strlen(search_key); // 移动到值的位置
    while (*key_pos == ' ' || *key_pos == '\t') key_pos++; // 跳过空白字符
    
    if (*key_pos != '"') return 0; // 值必须以引号开始
    key_pos++; // 跳过开始引号
    
    uint8_t i = 0;
    while (*key_pos != '"' && *key_pos != '\0' && i < max_len - 1) {
        value[i++] = *key_pos++;
    }
    value[i] = '\0';
    
    return (*key_pos == '"') ? 1 : 0; // 检查是否正确结束
}

/**
 * @brief 解析PID参数JSON字符串
 * @param json_str: JSON字符串，格式: {"kp":"值","ki":"值","kd":"值"}
 * @param params: 输出的PID参数结构体
 * @return 1=解析成功, 0=解析失败
 */
uint8_t json_parse_pid_params(const char* json_str, PID_Params_t* params) {
    if (!json_str || !params) return 0;
    
    char value_str[16];
    params->valid = 0;
    
    // 解析kp参数
    if (!json_find_value(json_str, "kp", value_str, sizeof(value_str))) return 0;
    params->kp = atof(value_str);
    
    // 解析ki参数
    if (!json_find_value(json_str, "ki", value_str, sizeof(value_str))) return 0;
    params->ki = atof(value_str);
    
    // 解析kd参数
    if (!json_find_value(json_str, "kd", value_str, sizeof(value_str))) return 0;
    params->kd = atof(value_str);
    
    params->valid = 1; // 标记解析成功
    return 1;
}
