//
// Created by 点点苍穹 on 25-7-23.
//

#ifndef KEY_H
#define KEY_H

#include "sys.h"
#include "ui.h"

// 按键事件定义
typedef enum {
    KEY_EVENT_NONE = 0,
    KEY_EVENT_LEFT,         // KEY1 - 向左翻页
    KEY_EVENT_RIGHT,        // KEY2 - 向右翻页
    KEY_EVENT_AUTO,         // KEY3 - 自动翻页切换
    KEY_EVENT_MOTOR_TOGGLE, // KEY3长按 - 电机开关
    KEY_EVENT_CALIBRATE     // KEY1长按 - 机械中值校准
} Key_Event_t;

// 按键状态定义
typedef struct {
    uint8_t key1_pressed;       // KEY1按下状态
    uint8_t key2_pressed;       // KEY2按下状态
    uint8_t key3_pressed;       // KEY3按下状态
    uint32_t key1_press_time;   // KEY1按下时间
    uint32_t key2_press_time;   // KEY2按下时间
    uint32_t key3_press_time;   // KEY3按下时间
    uint8_t key1_long_handled;  // KEY1长按已处理标志
    uint8_t key3_long_handled;  // KEY3长按已处理标志
} Key_State_t;

// 函数声明
void key_init(void);                    // 按键初始化
void key_process_event(Key_Event_t event); // 按键事件处理
// 注意：KeyScanTimerCallback由FreeRTOS定时器调用，key_scan功能已集成到回调中

extern osMessageQueueId_t KeyEventQueueHandle;
extern Key_State_t key_state;

#endif //KEY_H
