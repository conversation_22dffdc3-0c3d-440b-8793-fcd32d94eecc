#include "tracking.h"
#include "pid.h"

/**************************************************************************
�������ܣ�ѭ��ģ��IO�ڳ�ʼ��
��ڲ�������
����  ֵ����
**************************************************************************/
void Tracking_Init(void)
{
	
		GPIO_InitTypeDef GPIO_InitStructure;

		RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB,ENABLE);//ʹ��A\Bʱ��
		RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE);//ʹ��A\Bʱ��
		
		GPIO_InitStructure.GPIO_Pin  = GPIO_Pin_0|GPIO_Pin_1;//PB 0 1
		GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPD; //���ó���������
		GPIO_Init(GPIOB, &GPIO_InitStructure);
		
		GPIO_InitStructure.GPIO_Pin  = GPIO_Pin_6|GPIO_Pin_7;
		GPIO_Init(GPIOA, &GPIO_InitStructure);
} 



/**************************************************************************
�������ܣ�ѭ��ģ�飬�߼�����
��ڲ�������
����  ֵ����
**************************************************************************/	
extern float yaw;
void Tracking_detection(void)
{
	if (~(abs(pid.left)>0||abs(pid.right)))
	{
	if((Left_Tracking==0&&Right_Tracking == 0&&Mid_Tracking==0&&EndRight_Tracking==0))
	{
		pid.Angle_turn = pid.Angle_turn;
	}
	else if(Left_Tracking==1&&(Right_Tracking == 1||Mid_Tracking==1))  //�����ת
	{
		pid.Angle_turn -=0.5;
	}
	else if(EndRight_Tracking==1&&(Right_Tracking == 1||Mid_Tracking==1))//�����ת
	{
		pid.Angle_turn +=0.5;
	}
		else if(Mid_Tracking==1&&Left_Tracking==0)  //�����ת
	{
		pid.Angle_turn -=0.2;
	}
			else if(Right_Tracking==1&&EndRight_Tracking==0)  //�����ת
	{
		pid.Angle_turn +=0.2;
	}

//	else if(Left_Tracking==1&&Right_Tracking == 0&&Mid_Tracking==0)  //��ת,�����ٶȱ�0
//	{
//		pid.Angle_turn +=2.5;
//	}
//	else if(Left_Tracking==0&&Right_Tracking == 1&&Mid_Tracking==0)//��ת
//	{
//		pid.Angle_turn -=2.5;
//	}
	else if(EndRight_Tracking==1)  //ֱ��
	{
		pid.Angle_turn +=0.5;
	}
		else if(Left_Tracking==1)  //ֱ��
	{
		pid.Angle_turn -=0.5;
	}
//		else if(Mid_Tracking==1&&Right_Tracking==1)  //ֱ��
//	{
//		
//	}
//		else if(Mid_Tracking==1&&Right_Tracking==0)  //ֱ��
//	{
//		
//	}
//	else if(EndRight_Tracking==1)
//		pid.Angle_turn +=45;
	else
	{

	}
}
		else
	{
		pid.Angle_turn =pid.left-pid.right;
	}
  
}




