#include "bluetooth.h"
#include "led.h"
#include "pid.h"
/**************************************************************************
�������ܣ�����3��ʼ��
��ڲ����� bound:������
����  ֵ����
**************************************************************************/
void uart3_init(u32 bound)
{  	 
	  //GPIO�˿�����
  GPIO_InitTypeDef GPIO_InitStructure;
	USART_InitTypeDef USART_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	 
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);	//ʹ��UGPIOBʱ��
  RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3, ENABLE);	//ʹ��USART3ʱ��
	//USART3_TX  
  GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10; //PB.10
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;	//�����������
  GPIO_Init(GPIOB, &GPIO_InitStructure);
   
  //USART3_RX	  
  GPIO_InitStructure.GPIO_Pin = GPIO_Pin_11;//PB11
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;//��������
  GPIO_Init(GPIOB, &GPIO_InitStructure);

  //Usart3 NVIC ����
  NVIC_InitStructure.NVIC_IRQChannel = USART3_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=0 ;//��ռ���ȼ�
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;		//�����ȼ�
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;			//IRQͨ��ʹ��
	NVIC_Init(&NVIC_InitStructure);	//����ָ���Ĳ�����ʼ��VIC�Ĵ���
   //USART ��ʼ������
	USART_InitStructure.USART_BaudRate = bound;//���ڲ�����
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;//�ֳ�Ϊ8λ���ݸ�ʽ
	USART_InitStructure.USART_StopBits = USART_StopBits_1;//һ��ֹͣλ
	USART_InitStructure.USART_Parity = USART_Parity_No;//����żУ��λ
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;//��Ӳ������������
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;	//�շ�ģʽ
  USART_Init(USART3, &USART_InitStructure);     //��ʼ������3
  USART_ITConfig(USART3, USART_IT_RXNE, ENABLE);//�������ڽ����ж�
  USART_Cmd(USART3, ENABLE);                    //ʹ�ܴ���3 
}

/**************************************************************************
�������ܣ�����3�����ж�
��ڲ�������
����  ֵ����
**************************************************************************/
//u8 right_turn=0,left_turn=0;
u8 USART3_IRQHandler(void)
{	
	static	int uart_receive=0;//����������ر���
	//printf("usart3:%d/n",USART_GetITStatus(USART3, USART_IT_RXNE));
	if(USART_GetITStatus(USART3, USART_IT_RXNE) != RESET) //���յ�����
	{	  
	 
		uart_receive=USART_ReceiveData(USART3); 

	   switch (uart_receive)
		 {
			 case  '1':
				 pid.move = 35;LED0=0;Flag_Qian=1;Flag_Hou=0;Flag_Left=0;Flag_Right=0;        break;//���
		   case  '2': 
				 pid.move = -35;LED0=0;Flag_Qian=0;Flag_Hou=1;Flag_Left=0;Flag_Right=0;      break;//��ǰ
			 case  '3':
				 pid.left +=0.2; LED0=0;Flag_Qian=0;Flag_Hou=0;Flag_Left=1;Flag_Right=0;  		break; //��ת
			 case  '4':	
				 pid.right -=0.2;LED0=0;Flag_Qian=0;Flag_Hou=0;Flag_Left=0;Flag_Right=1;    break; //��ת		
			 case  '5': 
				 pid.right -=0;pid.left -=0;pid.move = 0;LED0=0;Flag_Qian=0;Flag_Hou=0;Flag_Left=0;Flag_Right=0;         break;; //ԭ��
			 default: 
				 LED0 = ~LED0;				
			 break;//��תС��
			 /*
			 С��  ������ ��ֵΪ--->����˶�
			              ��ֵΪ--->��ǰ�˶�         ��������OLEDΪ������
						 
			 */
		 }
		 
	}
	
	return uart_receive;
} 



