#include "key.h"
#include "delay.h"

/**************************************************************************
�������ܣ�������ʼ������
��ڲ�������
����  ֵ����
**************************************************************************/	 
void KEY_Init(void)
{
	
	GPIO_InitTypeDef GPIO_InitStructure;

 	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE);//ʹ��PORTA,PORTCʱ��

	GPIO_PinRemapConfig(GPIO_Remap_SWJ_JTAGDisable, ENABLE);//�ر�jtag��ʹ��SWD��������SWDģʽ����
	
	GPIO_InitStructure.GPIO_Pin  = GPIO_Pin_2|GPIO_Pin_3|GPIO_Pin_4|GPIO_Pin_5;//PA15
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU; //���ó���������
 	GPIO_Init(GPIOA, &GPIO_InitStructure);
	PAout(2)=0;PAout(3)=0;PAout(4)=0;PAout(5)=0;
	

} 
/**************************************************************************
�������ܣ�����ɨ�躯����
��ڲ������Ƿ�֧������������1��֧�֣�0����֧��
����  ֵ����
**************************************************************************/	 
u8 KEY_Scan(u8 mode)
{	 
	static u8 key_up=1;//�������ɿ���־
	if(mode)key_up=1;  //֧������		  
	if(key_up&&(KEY0==0||KEY1==0||KEY2==0||KEY3==0))
	{
		delay_ms(50);//ȥ���� 
		key_up=0;
		if(KEY0==1){ //printf("%i",KEY0);
			return KEY0_PRES;}
		else if(KEY1==1)return KEY1_PRES;
		else if(KEY2==1)return KEY2_PRES;
		else if(KEY3==1) return KEY3_PRES; 
	}else if(KEY0==0&&KEY1==0&&KEY2==0&&KEY3==0)key_up=1; 	     
	return 0;// �ް�������
	
}
