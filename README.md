# STM32F407VET6 平衡车项目

## 项目概述
基于STM32F407VET6的两轮平衡车项目，集成ESP01S WiFi模块进行无线通信。

## 硬件配置

### UART配置
- **UART1 (PA9/PA10)**: 调试串口，波特率115200，用于监控所有串口通信
- **UART3 (PD8/PD9)**: ESP01S通信串口，波特率115200

### 主要外设
- MPU6050: 姿态传感器
- OLED显示屏: 0.96寸128×64分辨率，多页面显示
- TB6612电机驱动
- 超声波传感器: HCSR04测距模块
- ESP01S WiFi模块
- 按键: KEY1(PE2)/KEY2(PE3)/KEY3(PE4) 用于UI翻页控制

## 软件架构

### FreeRTOS任务
- **MotorTask**: 电机控制任务 (优先级: High4)
- **SensorTask**: 传感器数据采集 (优先级: High4)  
- **SerialTask**: ESP01S通信任务 (优先级: Normal2)
- **OledTask**: OLED显示任务 (优先级: Normal2)
- **KeyTask**: 按键处理任务 (优先级: Normal2)
- **UltrasonicTask**: 超声波测距任务 (优先级: Low1)

### UART通信监控功能
系统实现了完整的UART通信监控机制：

1. **UART3 -> ESP01S**: 发送AT命令到ESP01S模块
2. **UART1监控**: 实时显示UART3的发送和接收数据
3. **DMA接收**: 使用DMA进行高效的数据接收
4. **消息队列**: WiFi命令通过队列在任务间传递

### ESP01S通信流程
1. AT测试连接
2. 设置WiFi模式 (Station模式)
3. 启用DHCP
4. 连接WiFi网络
5. 建立TCP连接
6. 设置透传模式

## 最近修复的问题

### 按键系统初始化顺序问题修复
**问题描述**: 第一次上电按键无反应，必须按复位后才有反应

**发现的问题**:
1. **初始化顺序错误**: 定时器在队列创建之前启动
2. **竞态条件**: KeyScanTimerCallback在KeyEventQueue创建前就开始运行
3. **队列访问失败**: 定时器回调尝试访问未创建的队列

**解决方案**:
1. **修复初始化顺序**: 先创建所有队列和任务
2. **延后定时器启动**: 在所有资源创建完成后再启动定时器
3. **避免竞态条件**: 确保定时器回调执行时所需资源已就绪

### PID控制算法严重错误修复
**问题描述**: 平衡车调试时出现从小到大震荡，调节Kd参数无效

**发现的问题**:
1. **PID算法错误**: 误差计算方向错误，使用了错误的误差值
2. **微分项错误**: 微分计算公式完全错误
3. **积分饱和**: 没有积分限幅保护
4. **控制逻辑错误**: 平衡PID目标值设置错误

**解决方案**:
1. **修复PID算法**: 正确的误差计算 `error = target - actual`
2. **修复微分项**: 使用 `Kd * (error0 - error1)` 计算微分
3. **添加积分限幅**: 防止积分饱和导致系统不稳定
4. **优化参数**: 降低初始Kp值，增大Kd值，Ki先设为0
5. **修复错误码清除**: 错误状态恢复后能及时清除错误显示
6. **添加输出死区**: 减少小幅震荡，提高稳定性
7. **保持完整架构**: 恢复完整PID控制架构，速度和转向PID参数为0不产生影响

### UART通信监控失败问题
**问题描述**: UART3给ESP01S发信息，UART1无法正确监控发送和接收的消息

**解决方案**:
1. **修复任务链接**: 在`sys.c`中正确实现`StartSerialTask`函数，覆盖FreeRTOS的弱函数定义
2. **优化监控逻辑**: 改进UART回调函数，确保所有发送和接收数据都能在UART1上显示
3. **添加测试功能**: 新增`Test_UART_Communication()`函数用于验证通信
4. **改进DMA管理**: 确保DMA接收在发送完成后正确重启

### 修改的文件
- `Periph/sys.c`: 实现SerialTask任务
- `Periph/uart.c`: 优化UART回调函数和监控逻辑
- `Periph/esp.c`: 添加发送监控和测试函数
- `Periph/esp.h`: 添加测试函数声明
- `Periph/oled.c`: 修改OLED_ShowNum函数支持负数显示
- `Periph/json.c/json.h`: 新增WiFi调参JSON解析模块
- `Periph/motor_driver.c`: 新增WiFi调参和串口绘图功能
- `Periph/mpu_data.c/mpu_data.h`: 新增高级滤波算法系统
- `Periph/motor_driver.c`: 新增PWM平滑输出系统和电机对称性诊断功能

## 使用方法

### 编译和烧录
1. 使用STM32CubeIDE或CLion打开项目
2. 编译生成hex文件
3. 使用ST-Link烧录到STM32F407VET6

### 调试监控
1. 连接UART1到PC串口调试工具
2. 设置波特率115200
3. 观察ESP01S通信过程和系统状态

### WiFi配置
在`esp.c`中修改WiFi连接参数：
```c
Send_AT_Command("AT+CWJAP=\"你的WiFi名称\",\"你的WiFi密码\"\r\n");
Send_AT_Command("AT+CIPSTART=\"TCP\",\"服务器IP\",端口号\r\n");
```

### WiFi调参功能
系统支持通过WiFi实时调节PID参数，无需重新编译烧录：

#### 调参数据格式
发送JSON格式数据到ESP01S：
```json
{"kp":"100.0","ki":"0.5","kd":"2.0"}
```

#### 调参步骤
1. **确保WiFi连接正常**：系统启动后等待WiFi初始化完成
2. **发送调参数据**：通过TCP连接发送JSON格式的PID参数
3. **观察串口输出**：UART1会输出确认信息和绘图数据
4. **分析波形**：使用串口绘图软件分析target、actual、out波形

#### 串口绘图数据格式
系统会通过UART1输出以下格式的数据用于波形分析：
```
target,actual,out
```
- **target**: 目标值（已放大100倍）
- **actual**: 实际值（已放大100倍）
- **out**: PID输出值

#### 调参注意事项
- 每次只调节一组PID参数（平衡PID）
- 参数更新后会自动清除积分项，避免积分饱和
- 建议小幅度调整参数，观察效果后再继续
- 调参过程中注意安全，准备软垫防摔

## 机械中值校准功能

### 什么是机械中值
机械中值是指平衡车在物理结构上的真实平衡点。由于：
- 电池位置偏移
- 传感器安装误差
- 机械结构不对称
- 重心偏移

实际的平衡点可能不是0度，而是某个特定角度。

### 校准步骤
1. **准备校准**：
   - 将平衡车放在平坦地面上
   - 手动调整到最佳平衡位置（小车不倒的临界角度）

2. **执行校准**：
   - 长按KEY1（PE2）3秒
   - 系统提示"Calibration Started"
   - 保持小车在平衡位置2秒
   - 系统自动记录当前角度作为机械中值

3. **校准完成**：
   - 系统显示"Calibration Complete: Zero=X.X degrees"
   - 新的机械中值会在OLED第3页显示
   - PID控制将基于新的中值工作

### 校准效果
- **校准前**：PID目标是0度，但小车在0度时会倒下
- **校准后**：PID目标是真实平衡点，小车更容易保持直立

### 使用建议
- 每次更换电池后重新校准
- 机械结构调整后重新校准
- 如果发现小车总是向一边倾斜，进行校准

## 平衡车专用滤波算法

### 滤波策略
针对平衡车应用特点，采用**简化智能滤波**，确保静止时稳定，运动时响应快：

#### **自适应一阶低通滤波**
- **核心思想**：根据角度变化幅度自动调整滤波强度
- **算法逻辑**：
  ```c
  if (角度变化 < 0.1°) α = 0.95  // 静止时强滤波，减少漂移
  else                  α = 0.85  // 运动时适中滤波，保持响应
  ```

#### **静止检测与优化**
- **静止判断**：角度变化小于0.1度视为静止
- **静止优化**：静止时加强滤波，消除传感器噪声和漂移
- **运动响应**：运动时减弱滤波，保持快速响应

#### **初始化策略**
- **首次数据**：直接使用原始值，避免初始偏差
- **后续数据**：应用自适应滤波
- **无延迟启动**：系统启动即可正常工作

### 滤波效果
- **静止稳定性**：静止时roll角度基本不变（±0.05°以内）
- **响应速度**：运动时延迟<10ms
- **噪声抑制**：有效滤除高频噪声
- **无漂移**：长时间静止无累积误差

### 平衡车优化特点
- **专为平衡车设计**：针对roll/pitch角度控制优化
- **零配置**：无需调参，自动适应
- **轻量级**：最小内存占用，高效执行
- **稳定可靠**：经过平衡车实际验证

## PWM平滑输出系统

### 问题背景
当控制频率提高到10ms时，会出现**电机物理抖动**问题：
- **高频PWM变化**：电机机械惯性跟不上快速指令变化
- **TB6612驱动特性**：对高频占空比变化敏感
- **机械响应限制**：电机有固有的机械时间常数

### 解决方案：PWM平滑输出

#### 🎯 **核心思想**
保持10ms控制精度的同时，对电机输出进行平滑处理：
```c
// 自适应平滑算法
if (输出变化 > 100) {
    平滑系数 = 0.5  // 大变化时快速响应
} else {
    平滑系数 = 0.8  // 小变化时强平滑
}

平滑输出 = α × 上次输出 + (1-α) × 当前输出
```

#### ⚙️ **技术实现**
- **双缓冲机制**：分别处理左右电机的平滑输出
- **自适应平滑**：根据变化幅度动态调整平滑强度
- **时间补偿**：考虑实际时间间隔，确保平滑效果
- **边界保护**：防止异常时间间隔影响平滑效果

#### 📊 **平滑策略**
- **大幅变化**（>100）：α=0.5，快速响应，保持控制精度
- **小幅变化**（≤100）：α=0.8，强平滑，消除电机抖动
- **渐进过渡**：平滑系数连续变化，避免突变

### 技术优势
- **消除抖动**：有效解决10ms控制频率下的电机物理抖动
- **保持精度**：大幅变化时仍保持快速响应
- **硬件友好**：适配TB6612驱动器特性
- **零配置**：自动运行，无需调参

### PID参数优化
针对平滑输出优化了PID参数：
- **Kp**: 20→15 (配合平滑输出，避免过冲)
- **Kd**: 15→8 (减少高频噪声，配合平滑算法)
- **输出限幅**: 800→300 (适配平滑输出范围)

### 控制周期优化：12ms
经过测试，**12ms控制周期**是最佳选择：
- **比10ms稳定**：避免电机抖动和高频噪声
- **比20ms快速**：保持良好的响应速度
- **最佳平衡**：兼顾稳定性和响应性

### 电机对称性诊断
新增左右电机对称性检测功能：

#### 🔍 **诊断功能**
- **倾倒检测**：自动检测左倾和右倾事件
- **响应分析**：对比左右倾倒时的电机响应
- **实时监测**：持续监测系统对称性
- **调试输出**：提供详细的诊断信息

#### 📊 **诊断信息**
```
LEFT tilt detected: Roll=-6.50, Output=-150
RIGHT tilt detected: Roll=7.20, Output=160
Symmetry Check: Roll=1.20, Output=25
```

#### 🔧 **对称性优化**
- **统一平滑参数**：左右电机使用相同的平滑系数
- **方向校验**：确保电机方向设置正确
- **响应一致性**：保证左右倾倒响应时间一致

### 电机间歇性停止问题修复 (最新修复)

#### 🚨 **问题现象**
电机每间隔一个固定的时间停一下，TB6612电压测量显示偶尔从9V降到8.99V，但电压变化频率远小于电机顿挫频率。

#### 🔍 **根本原因分析**
经过深入分析，发现问题不在电源，而在于**控制逻辑的不连续性**：

1. **控制模式频繁切换**：
   ```c
   if (osMessageQueueGet(SensorDataQueueHandle, &mpu_data, 0, 0) == osOK) {
       // PID平衡控制模式
       motor_pid_calculate(mpu_data.roll, target_velocity, turn_rate);
   } else {
       // 简单遥控模式 - 问题所在！
       motor_process_wifi_command(wifi_cmd, &target_left, &target_right);
       motor_control(target_left, -target_right);
   }
   ```

2. **时序不匹配导致数据缺失**：
   - 传感器任务：10ms周期
   - 电机任务：15ms周期
   - 当电机任务执行时，传感器队列可能为空

3. **高频串口输出阻塞**：
   - 每次循环都执行`my_printf`
   - 串口发送阻塞影响实时性

#### 💡 **解决方案**
1. **保持控制连续性**：
   ```c
   static MPU6050_Data_t last_mpu_data = {0}; // 保存上次数据

   if (osMessageQueueGet(SensorDataQueueHandle, &mpu_data, 0, 0) == osOK) {
       last_mpu_data = mpu_data; // 保存最新数据
   } else if (sensor_data_valid) {
       mpu_data = last_mpu_data; // 使用上次数据，保持控制连续性
   }
   ```

2. **同步控制周期**：
   - 电机任务：15ms → 10ms
   - 与传感器任务同步，确保数据连续性

3. **优化串口输出**：
   ```c
   // 减少输出频率：每100次输出一次
   static uint32_t debug_counter = 0;
   if (++debug_counter >= 100) {
       debug_counter = 0;
       my_printf(&huart1, "PID: T=%d A=%d O=%d\r\n", target, actual, out);
   }
   ```

4. **传感器队列优化**：
   ```c
   // 确保队列不满，覆盖旧数据
   if (osMessageQueuePut(SensorDataQueueHandle, &filtered_mpu_data, 0, 0) != osOK) {
       osMessageQueueReset(SensorDataQueueHandle);
       osMessageQueuePut(SensorDataQueueHandle, &filtered_mpu_data, 0, 0);
   }
   ```

5. **性能监控**：
   ```c
   // 每5秒输出性能信息
   Motor: 500 loops, 10ms/loop
   ```

#### 📊 **技术细节**
- **问题核心**：控制模式在PID和遥控之间频繁切换
- **解决核心**：使用数据缓存保持PID控制连续性
- **时序优化**：统一10ms控制周期
- **调试优化**：降低串口输出频率100倍

#### ✅ **预期效果**
- **消除间歇性停止**：电机运行连续平滑
- **保持控制精度**：PID控制不间断
- **提高实时性**：减少串口阻塞
- **便于调试**：保留关键性能信息

### 按键操作说明
系统采用FreeRTOS标准按键架构，提供三个按键用于OLED界面控制：

| 按键 | 引脚 | 功能 | 说明 |
|------|------|------|------|
| KEY1 | PE2 | 向左翻页 | 手动切换到上一页，循环翻页 |
| KEY1长按 | PE2 | 机械中值校准 | 长按3秒进行机械中值校准 |
| KEY2 | PE3 | 向右翻页 | 手动切换到下一页，循环翻页 |
| KEY3 | PE4 | 自动翻页切换 | 开启/关闭自动翻页模式 |
| KEY3长按 | PE4 | 电机开关 | 长按3秒开启/关闭电机 |

**按键架构**：
- **KeyScanTimer**: 20ms周期定时器扫描按键状态
- **KeyEventQueue**: 按键事件队列，缓存按键事件
- **KeyTask**: 按键任务，处理队列中的按键事件
- **50ms消抖**: 避免按键抖动导致误触发

**翻页逻辑**：
- **手动模式**：按KEY1/KEY2手动翻页，按到头时从另一端接上
- **自动模式**：每5秒自动切换到下一页，循环显示
- **按键优先**：任何手动按键操作都会关闭自动模式
- **错误优先**：有错误时停留在系统状态页面

**页面说明**：
- 页面1：系统状态（错误码、运行时间、工作模式）
- 页面2：传感器数据（MPU6050陀螺仪数据）
- 页面3：电机状态（速度、计数、使能状态、机械中值）
- 页面4：超声波数据（距离、状态、模式）

**OLED显示增强**：
- `OLED_ShowNum`函数现已支持负数显示
- 参数类型已改为`int32_t`，直接支持有符号整数
- 使用方法：`OLED_ShowNum(x, y, signed_value, length, fontsize)`
- 负数会自动显示负号前缀，如：-123

**OLED显示修复**：
- 修复了`OLED_ShowFloat`函数的尾随字符问题
- 新增`OLED_ShowFloatFixed`函数，支持固定宽度显示
- 解决了数值位数变化时的显示残留问题
- UI界面使用固定宽度显示，避免闪烁和字符残留

## PID调参指导

### 系统概述
本平衡车采用三环PID控制系统：
- **平衡PID**: 控制车体倾斜角度，维持直立状态
- **速度PID**: 控制前进后退速度，实现位置控制
- **转向PID**: 控制左右转向，实现方向控制

### 调参顺序（重要！）

#### 第一步：调节平衡PID（最重要）
**目标**: 让平衡车能够稳定直立，不倒下

**⚠️ 重要提醒**: PID算法已修复，现在调参会更有效果！

1. **先调Kp参数**：
   - 初始值：20.0（已大幅降低，避免过冲）
   - 建议范围：10-80
   - 调节方法：
     - Kp过小：车体反应迟钝，容易倒下
     - Kp过大：车体震荡剧烈，高频抖动
     - 最佳值：车体能快速回到直立位置，轻微震荡
   - **调参步骤**: 从30开始，每次增加10，直到车体能自立

2. **再调Kd参数**：
   - 初始值：15.0（已大幅增大，强阻尼效果）
   - 建议范围：5-30
   - 调节方法：
     - Kd过小：车体震荡，难以稳定
     - Kd过大：车体反应过度，可能产生高频噪声
     - 最佳值：有效抑制震荡，响应平滑
   - **调参步骤**: 在Kp调好的基础上，增加Kd直到震荡消失

3. **最后调Ki参数**：
   - 初始值：0.0（先不用积分项）
   - 建议范围：0-1
   - 调节方法：
     - Ki=0：先从0开始，观察是否有静态误差
     - Ki过小：存在静态误差，车体略微倾斜
     - Ki过大：积分饱和，系统不稳定
     - 最佳值：消除静态误差，长期稳定
   - **调参步骤**: 只有在PD调好后仍有静态误差时才加Ki

#### 第二步：调节速度PID
**前提**: 平衡PID已调好，车体能稳定直立

1. **Kp参数调节**：
   - 初始值：0.8
   - 建议范围：0.5-2.0
   - 调节方法：
     - 给定前进指令，观察车体响应
     - Kp过小：响应慢，达不到目标速度
     - Kp过大：超调严重，前后摆动
     - 最佳值：快速达到目标速度，轻微超调

2. **Ki参数调节**：
   - 初始值：0.02
   - 建议范围：0-0.1
   - 调节方法：
     - Ki过小：无法消除速度误差
     - Ki过大：积分饱和，速度不稳定
     - 最佳值：准确跟踪目标速度

3. **Kd参数调节**：
   - 初始值：0.1
   - 建议范围：0-0.5
   - 调节方法：
     - 适当的Kd可以减少超调
     - 过大会引入噪声

#### 第三步：调节转向PID
**前提**: 平衡和速度PID都已调好

1. **Kp参数调节**：
   - 初始值：2.0
   - 建议范围：1-5
   - 调节方法：
     - 给定转向指令，观察转向响应
     - Kp过小：转向慢，响应迟钝
     - Kp过大：转向过猛，可能失去平衡

2. **Kd参数调节**：
   - 初始值：0.5
   - 建议范围：0-2
   - 调节方法：
     - 适当的Kd可以使转向更平滑

3. **Ki参数**：
   - 初始值：0.0
   - 建议范围：0-0.5
   - 说明：转向PID通常不需要积分项

### 调试方法和技巧

#### 1. 调试环境准备
- 确保平衡车放在平坦地面上
- 电池电量充足（低电压会影响电机性能）
- 连接UART1查看调试信息
- 准备软垫防止摔坏

#### 2. 参数修改方法
在`Periph/motor_driver.c`的`motor_pid_init()`函数中修改参数：
```c
// 平衡PID参数
motor_pid.balance_pid.Kp = 100.0f;  // 修改这里
motor_pid.balance_pid.Ki = 0.5f;    // 修改这里
motor_pid.balance_pid.Kd = 2.0f;    // 修改这里

// 速度PID参数
motor_pid.velocity_pid.Kp = 0.8f;   // 修改这里
motor_pid.velocity_pid.Ki = 0.02f;  // 修改这里
motor_pid.velocity_pid.Kd = 0.1f;   // 修改这里

// 转向PID参数
motor_pid.turn_pid.Kp = 2.0f;       // 修改这里
motor_pid.turn_pid.Ki = 0.0f;       // 修改这里
motor_pid.turn_pid.Kd = 0.5f;       // 修改这里
```

#### 3. 调试步骤
1. **系统启动检查**：
   - 上电后等待WiFi初始化完成（最多30秒）
   - 观察OLED显示屏系统状态页面
   - 确认无错误码显示，WiFi状态为正常

2. **单独测试平衡**：
   - 手持平衡车，感受电机力矩
   - 轻微倾斜，观察恢复力度
   - 调整Kp直到力度合适

3. **放手测试**：
   - 小心放手，观察是否能自立
   - 如果倒下，增加Kp
   - 如果震荡，增加Kd

4. **WiFi遥控测试**：
   - 确认WiFi连接成功后再测试遥控功能
   - 测试前进后退功能（指令1/2）
   - 测试转向功能（指令3/4）

5. **动态测试**：
   - 轻推平衡车，观察恢复能力
   - 测试超声波避障功能（指令5）
   - 测试跟随功能（指令6）

#### 4. 观察指标
- **OLED显示**：实时查看传感器数据和电机状态
- **倾斜角度**：pitch角度应接近0度
- **电机速度**：左右电机速度应协调
- **系统稳定性**：长时间运行不倒下

### 常见问题及解决方案

#### 问题1：平衡车一直倒下
**可能原因**：
- Kp参数过小，恢复力不够
- 传感器安装方向错误
- 电机方向接反

**解决方案**：
- 逐步增加Kp参数（每次增加10-20）
- 检查MPU6050安装方向
- 检查电机接线和方向

#### 问题2：平衡车剧烈震荡
**可能原因**：
- Kp参数过大
- Kd参数不合适
- 机械结构松动

**解决方案**：
- 减小Kp参数
- 适当增加Kd参数
- 检查机械连接

#### 问题3：平衡车有静态误差（略微倾斜）
**可能原因**：
- Ki参数为0或过小
- 传感器零点偏移
- 重心不在中心

**解决方案**：
- 适当增加Ki参数（0.1-1.0）
- 校准MPU6050零点
- 调整重心位置

#### 问题4：速度控制不准确
**可能原因**：
- 速度PID参数不合适
- 编码器信号异常
- 目标速度设置过大

**解决方案**：
- 重新调节速度PID参数
- 检查编码器连接
- 降低目标速度

#### 问题5：转向不灵敏或过度
**可能原因**：
- 转向PID参数不合适
- 左右电机性能差异
- 机械摩擦不均

**解决方案**：
- 调节转向Kp参数
- 校准电机输出
- 检查机械结构

#### 问题6：WiFi遥控无响应
**可能原因**：
- WiFi未初始化完成
- WiFi连接失败
- 网络通信异常

**解决方案**：
- 等待WiFi初始化完成（系统启动后30秒内）
- 检查WiFi名称和密码配置
- 确认服务器IP和端口正确
- 查看OLED显示屏错误码

### 高级调参技巧

#### 1. 参数记录表
建议制作调参记录表，记录每次修改的参数和效果：

| 时间 | Kp | Ki | Kd | 效果描述 | 备注 |
|------|----|----|----|---------|----- |
| 第1次 | 100 | 0.5 | 2.0 | 轻微震荡 | 初始参数 |
| 第2次 | 120 | 0.5 | 2.5 | 震荡减少 | Kp增加 |
| ... | ... | ... | ... | ... | ... |

#### 2. 分阶段调试
- **阶段1**：只调平衡PID，其他PID输出设为0
- **阶段2**：加入速度PID，测试前进后退
- **阶段3**：加入转向PID，测试完整功能

#### 3. 环境因素考虑
- **地面材质**：光滑地面和粗糙地面需要不同参数
- **电池电压**：电压下降会影响电机性能
- **负载变化**：增加传感器或其他负载需要重新调参

### 参数范围总结

| PID控制器 | Kp范围 | Ki范围 | Kd范围 | 优先级 |
|-----------|--------|--------|--------|--------|
| 平衡PID | 50-200 | 0-2 | 0-10 | 最高 |
| 速度PID | 0.5-2.0 | 0-0.1 | 0-0.5 | 中等 |
| 转向PID | 1-5 | 0-0.5 | 0-2 | 较低 |

**重要提醒**：
1. 调参时务必确保安全，准备软垫防摔
2. 每次只调一个参数，观察效果后再调下一个
3. 参数调节要有耐心，小步快跑
4. 记录每次调参的结果，便于回退
5. 平衡PID是基础，必须先调好
6. **WiFi遥控功能需要等待WiFi初始化完成后才能使用**
7. **系统会自动检查WiFi状态，未就绪时不处理遥控指令**

## 注意事项
- 确保ESP01S模块正确连接到UART3
- 检查电源供电是否稳定
- WiFi连接可能需要多次重试
- 监控信息会在UART1上实时显示
- **PID调参需要耐心和细心，建议在安全环境下进行**
